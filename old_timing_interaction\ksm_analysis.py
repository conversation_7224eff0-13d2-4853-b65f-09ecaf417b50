import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Cell 1: Analysis for KSM_Merged_ads_v3_cleaned.csv

# Load the dataset
try:
    df1 = pd.read_csv('KSM_Merged_ads_v3_cleaned.csv')
except FileNotFoundError as e:
    print(e)
    print("Please ensure 'KSM_Merged_ads_v3_cleaned.csv' is in the same directory as the notebook.")
    raise

# Define the timing columns
timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

def get_timing_category(row):
    """Assign a timing category to a row based on boolean flags."""
    for col in timing_cols:
        if row[col]:
            return col
    return 'No Overlap'

# Apply the function to create the 'timing_category' column
df1['timing_category'] = df1.apply(get_timing_category, axis=1)

# Set up the plot
plt.figure(figsize=(12, 8))
sns.boxplot(
    x='timing_category',
    y='ABI MS Promo Uplift - rel',
    data=df1,
    order=timing_cols + ['No Overlap']
)

# Add plot titles and labels
plt.title('Impact of Promotion Timing on Sales Uplift (KSM Data)', fontsize=16)
plt.xlabel('Competitor Promotion Timing', fontsize=12)
plt.ylabel('Relative Sales Uplift', fontsize=12)
plt.xticks(rotation=45)
plt.grid(True, which='both', linestyle='--', linewidth=0.5)
plt.tight_layout()

# Show the plot
plt.show()

# Display the value counts for each category
print("Value counts for KSM data:")
print(df1['timing_category'].value_counts()) 
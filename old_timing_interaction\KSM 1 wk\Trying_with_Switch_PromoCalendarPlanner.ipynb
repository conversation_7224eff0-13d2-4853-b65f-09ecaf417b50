{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# GLOBAL SWITCH ───────────────\n", "# True  → keep one row per ABI–Competitor SKU pair  (melted)\n", "# False → aggregate all competitor SKUs back onto one ABI row (un-melted)\n", "'''\n", "When MELT_SKU_FLAG = True\n", "No grouping on competitor SKU → every competitor gets its own row.\n", "CSV name: ads_melted_v3.csv → row-count is the largest.\n", "When MELT_SKU_FLAG = False\n", "We groupby the ABI SKU (and promo meta) → competitor SKUs pipe-joined.\n", "CSV name: ads_unmelted_v3.csv → row-count is smaller.'''\n", "\n", "MELT_SKU_FLAG = False  # Set to False to test aggregation"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "177c4728-e89f-4565-b33f-7c75c5448ec5", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## CS started editting here\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# OLD → NEW\n", "# 'Markets' → 'Market Description'  # Column C\n", "# 'POLE INTERNE' → 'POLE INTERNE'  # Column L (unchanged)\n", "# 'CONDITIONNEMENT' → 'STANDARDIZED PACK TYPE'  # Column M\n", "# 'NBR UNITE' → 'STANDARDIZED NUMBER IN PACK'  # Column O\n", "# 'GAMME' → 'Sub Brand'  # Column I\n", "# 'CTN UNIT' → 'STANDARDIZED PACK SIZE ML'  # Column N\n", "# 'Periods' → 'Period'  # Column P (not 'P End Date')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b8dadf81-fb90-44b5-a24b-8d9a5bd2a0a3", "showTitle": true, "tableResultSettingsMap": {}, "title": "Load A3 data"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def a3_load_and_preprocess(a3_path, retailer_filter=None):\n", "   \n", "    # Load A3 promo mapping file\n", "    a3_df = pd.read_excel(a3_path, engine='openpyxl',sheet_name='a3_distribution_march_2025')\n", "    \n", "    # Convert promo start date to datetime\n", "    a3_df['Date de début'] = pd.to_datetime(a3_df['Date de début'], dayfirst=True, errors='coerce')\n", "    \n", "    # Filter by retailer list\n", "    if retailer_filter:\n", "        a3_df = a3_df[a3_df['Enseigne'].isin(retailer_filter)]\n", "\n", "    # Filter out everything before April 1, 2022\n", "    a3_df = a3_df[a3_df['Date de début'] >= pd.Timestamp(\"2022-04-01\")]\n", "\n", "    # rename similar sku colum \n", "    a3_df.rename(columns={'Similar SKU Mapping': 'Similar_SKU_Mapping'}, inplace=True)\n", "\n", "    # Define and apply the promo mechanic mapping\n", "    mechanic_mapping = {\n", "        'LV': 'LV',\n", "        'Multi Buy': 'LV',\n", "        'FID': 'FID',\n", "        'Loyalty Cards': 'FID',\n", "        'Immediate': 'Immediate',\n", "        'RI': 'Immediate',\n", "        'No NIP': 'No NIP'\n", "    }\n", "    # Apply the mapping to merge categories, assuming 'ABI Mechanic' column exists\n", "    if 'ABI Mechanic' in a3_df.columns:\n", "        a3_df['ABI Mechanic'] = a3_df['ABI Mechanic'].map(mechanic_mapping).fillna(a3_df['ABI Mechanic'])\n", "\n", "    print(f\"A3 shape after filtering: {a3_df.shape}\")\n", "    return a3_df\n", "\n", "def niq_load_and_preprocess(niq_path, retailer_filter=None):\n", "    # read the 'RAW' sheet from the Excel file\n", "    niq = pd.read_excel(niq_path, engine='openpyxl', sheet_name='RAW')\n", "    \n", "    # Filter by retailer list\n", "    if retailer_filter:\n", "        niq = niq[niq['Market Description'].isin(retailer_filter)]\n", "\n", "    # convert to dates - now using direct date format instead of French text extraction\n", "    niq[\"date_week_end\"] = pd.to_datetime(niq[\"P End Date\"], errors=\"coerce\")\n", "    niq['date_week_start'] = niq['date_week_end'] - pd.DateOffset(days=6)\n", "\n", "    # Create clean column names with underscores\n", "    niq[\"Promo_Value\"] = niq[\"Promo Value TY\"].astype(float)\n", "    niq[\"Non_Promo_Price\"] = niq[\"Non Promo Price TY\"].astype(float)\n", "    niq[\"Promo_Price\"] = niq[\"Promo Price TY\"].astype(float)\n", "    niq[\"Non_Promo_Value\"] = niq[\"Non Promo Value TY\"].astype(float)\n", "    niq[\"Promo_Volume\"] = niq[\"Promo Volume TY\"].astype(float)\n", "    niq[\"Non_Promo_Volume\"] = niq[\"Non Promo Volume TY\"].astype(float)\n", "    niq[\"Base_Volume\"] = niq[\"Base Volume TY\"].astype(float)\n", "    niq[\"Base_Value_PPC\"] = niq[\"Base Value (PPC) TY\"].astype(float)\n", "    niq[\"Promo_Price_PPC\"] = niq[\"Promo Price (PPC) TY\"].astype(float)\n", "    niq[\"Non_Promo_Price_PPC\"] = niq[\"Non Promo Price (PPC) TY\"].astype(float)\n", "    \n", "    # Drop the original columns with spaces to avoid confusion\n", "    niq.drop(columns=[\"Promo Value TY\", \"Non Promo Price TY\", \"Promo Price TY\", \"Non Promo Value TY\", \n", "                      \"Promo Volume TY\", \"Non Promo Volume TY\", \"Base Volume TY\", \"Base Value (PPC) TY\"], inplace=True)\n", "    \n", "    # Create three datasets:\n", "    # 1. Total Beer per Retailer Dataset (POLE INTERNE is null)\n", "    niq_beer = niq[niq['POLE INTERNE'].isnull()].copy()\n", "    \n", "    # 2. Beer Pole Dataset (POLE INTERNE has values but brand/pack details are null)\n", "    beer_pole = niq[\n", "        (~niq['POLE INTERNE'].isnull()) & \n", "        (niq['BRAND'].isnull()) & \n", "        (niq['Sub Brand'].isnull())\n", "    ].copy()\n", "    \n", "    # 3. SKU level data (POLE INTERNE has values and brand/pack details are present)\n", "    niq_sku = niq[\n", "        (~niq['POLE INTERNE'].isnull()) & \n", "        (~niq['BRAND'].isnull()) & \n", "        (~niq['Sub Brand'].isnull())\n", "    ].copy()  \n", "\n", "    # Process SKU data\n", "    niq_sku['pack_type'] = niq_sku['STANDARDIZED PACK TYPE']\n", "    niq_sku['qty_in_pack'] = niq_sku['STANDARDIZED NUMBER IN PACK'].replace({\n", "        '12x': 'X(12-15)',\n", "        '15x': 'X(12-15)', \n", "        '20x': 'X(20-24)',\n", "        '24x': 'X(20-24)'\n", "    })\n", "    niq_sku['sku'] = (niq_sku['Sub Brand'] + ' ' + \n", "                    niq_sku['pack_type'] + ' ' + \n", "                    niq_sku['qty_in_pack'].str.replace('X', '') + 'X' + \n", "                    niq_sku['STANDARDIZED PACK SIZE ML'].astype(int).astype(str) + 'ML')\n", "\n", "    global sku_segment_mapping, beer_pole_renamed\n", "    \n", "    # Create SKU-to-segment mapping from data with POLE INTERNE\n", "    sku_segment_mapping = niq_sku[['sku', 'POLE INTERNE']].drop_duplicates()\n", "    sku_segment_mapping = sku_segment_mapping.rename(columns={'POLE INTERNE': 'segment'})\n", "    \n", "    # Process beer pole data for segment totals (from original niq, not niq_sku)\n", "    beer_pole_for_segments = niq[~niq['POLE INTERNE'].isnull() & niq['Sub Brand'].isnull()].copy()\n", "    if not beer_pole_for_segments.empty:\n", "        beer_pole_aggregated = beer_pole_for_segments.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({'Volume TY': 'sum', 'Value Sales TY': 'sum'})\n", "        beer_pole_aggregated['Volume TY'] = beer_pole_aggregated['Volume TY'].div(100)\n", "        beer_pole_renamed = beer_pole_aggregated.rename(columns={'POLE INTERNE': 'segment', 'Volume TY': 'segment_volume_hl', 'Value Sales TY': 'segment_value'})\n", "    \n", "    # NOW the groupby aggregation (this removes POLE INTERNE from niq_sku)\n", "    niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({\n", "        'Volume TY': np.sum, \n", "        'Value Sales TY': np.sum,\n", "        'Wtd Dist TY': np.max,\n", "        'Num Dist TY': np.max,\n", "        'ROS Value TY': np.max,\n", "        'Promo_Value': np.sum,\n", "        'Non_Promo_Price': np.max,\n", "        'Promo_Price': np.max,\n", "        'Non_Promo_Value': np.sum,\n", "        'Promo_Volume': np.sum,\n", "        'Non_Promo_Volume': np.sum,\n", "        'Base_Volume': np.sum,\n", "        'Base_Value_PPC': np.sum,\n", "        'Non_Promo_Price_PPC': np.max,\n", "        'Promo_Price_PPC': np.max,\n", "    })\n", "    \n", "    # Create derived columns\n", "    niq_sku['volume_hl'] = niq_sku['Volume TY'].div(100)\n", "    niq_sku['promo_volume_hl'] = niq_sku['Promo_Volume'].div(100)\n", "    niq_sku['non_promo_volume_hl'] = niq_sku['Non_Promo_Volume'].div(100)\n", "    niq_sku['base_volume_hl'] = niq_sku['Base_Volume'].div(100)\n", "    niq_sku['value_eur'] = niq_sku['Value Sales TY']\n", "    niq_sku['w_dist'] = niq_sku['Wtd Dist TY']\n", "    niq_sku['num_dist'] = niq_sku['Num Dist TY']\n", "    niq_sku['ros_value'] = niq_sku['ROS Value TY']\n", "\n", "    \n", "    niq_sku = niq_sku.drop(columns=['Volume TY', 'Value Sales TY', 'Wtd Dist TY', 'Num Dist TY', 'ROS Value TY'])\n", "\n", "    # Aggregate beer data (total beer level)\n", "    niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({\n", "        'Volume TY': np.sum, \n", "        'Value Sales TY': np.sum,\n", "        'Promo_Value': np.sum,\n", "        'Non_Promo_Price': np.max,\n", "        'Promo_Price': np.max,\n", "        'Non_Promo_Value': np.sum,\n", "        'Promo_Volume': np.sum,\n", "        'Non_Promo_Volume': np.sum,\n", "        'Base_Volume': np.sum,\n", "        'Base_Value_PPC': np.sum,\n", "        'Non_Promo_Price_PPC': np.max,\n", "        'Promo_Price_PPC': np.max,\n", "    })\n", "    niq_beer.rename(columns={'TOTAL BEER':'sku'}, inplace=True)\n", "    niq_beer['volume_hl'] = niq_beer['Volume TY'].div(100)\n", "    niq_beer['value_eur'] = niq_beer['Value Sales TY']\n", "    niq_beer['promo_volume_hl'] = niq_beer['Promo_Volume'].div(100)\n", "    niq_beer['non_promo_volume_hl'] = niq_beer['Non_Promo_Volume'].div(100)\n", "    niq_beer['base_volume_hl'] = niq_beer['Base_Volume'].div(100)\n", "    niq_beer['base_value_PPC'] = niq_beer['Base_Value_PPC'] \n", "    niq_beer['non_promo_price_PPC'] = niq_beer['Non_Promo_Price_PPC']\n", "    niq_beer['promo_price_PPC'] = niq_beer['Promo_Price_PPC']\n", "    niq_beer = niq_beer.drop(columns=['Volume TY', 'Value Sales TY', 'Base_Value_PPC', 'Non_Promo_Price_PPC', 'Promo_Price_PPC','Non_Promo_Volume', 'Promo_Volume', 'Base_Volume'])\n", "    \n", "    # Aggregate beer pole data (pole level) - include promo columns in groupby\n", "    beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({\n", "        'Volume TY': np.sum, \n", "        'Value Sales TY': np.sum,\n", "        'Promo_Value': np.sum,\n", "        'Non_Promo_Price': np.max,\n", "        'Promo_Price': np.max,\n", "        'Non_Promo_Value': np.sum,\n", "        'Promo_Volume': np.sum,\n", "        'Non_Promo_Volume': np.sum,\n", "        'Base_Volume': np.sum,\n", "        'Base_Value_PPC': np.sum,\n", "        'Non_Promo_Price_PPC': np.max,\n", "        'Promo_Price_PPC': np.max,\n", "        #Before aggreagting, need to calculate weighted average for Promo, then take max of that\n", "    })\n", "    beer_pole.rename(columns={'POLE INTERNE':'sku'}, inplace=True)\n", "    beer_pole['volume_hl'] = beer_pole['Volume TY'].div(100)\n", "    beer_pole['value_eur'] = beer_pole['Value Sales TY']\n", "    beer_pole['promo_volume_hl'] = beer_pole['Promo_Volume'].div(100)\n", "    beer_pole['non_promo_volume_hl'] = beer_pole['Non_Promo_Volume'].div(100)\n", "    beer_pole['base_volume_hl'] = beer_pole['Base_Volume'].div(100)\n", "    beer_pole['base_value_PPC'] = beer_pole['Base_Value_PPC']   \n", "    beer_pole['non_promo_price_PPC'] = beer_pole['Non_Promo_Price_PPC']\n", "    beer_pole['promo_price_PPC'] = beer_pole['Promo_Price_PPC']\n", "\n", "    beer_pole = beer_pole.drop(columns=['Volume TY', 'Value Sales TY','Base_Value_PPC', 'Non_Promo_Price_PPC', 'Promo_Price_PPC','Non_Promo_Volume', 'Promo_Volume', 'Base_Volume'])\n", "    \n", "    return niq_sku, niq_beer, beer_pole"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# load other data sets\n", "similar_skus = pd.read_csv(\"Latest_Similar_SKU_Mapping.csv\")\n", "weather = pd.read_csv(\"Weather.csv\")\n", "weather['date'] = pd.to_datetime(weather['date']).dt.tz_localize(None)\n", "holidays = pd.read_csv(\"Holidays.csv\")\n", "holidays['date'] = pd.to_datetime(holidays['date']).dt.tz_localize(None)\n", "retailers = pd.read_csv(\"Retailers.csv\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e6ffb2a6-82ff-4711-9426-18ac3d2a8af5", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# # load other data sets\n", "# similar_skus = pd.read_csv(\"/dbfs/FileStore/RevMan/Latest_Similar_SKU_Mapping.csv\")\n", "# weather = pd.read_csv(\"/dbfs/FileStore/RevMan/Weather.csv\")\n", "# weather['date'] = pd.to_datetime(weather['date']).dt.tz_localize(None)\n", "# holidays = pd.read_csv(\"/dbfs/FileStore/RevMan/Holidays.csv\")\n", "# holidays['date'] = pd.to_datetime(holidays['date']).dt.tz_localize(None)\n", "# # retailers = pd.read_csv(\"Retailers.csv\")\n", "# retailers = pd.read_excel('/dbfs/FileStore/RevMan/retailer_mapping.xlsx')\n", "\n", "# # read A3 data\n", "# a3_path = \"/dbfs/FileStore/RevMan/a3_distribution_march_2025_peter_wf.xlsx\"\n", "# selected_retailers = retailers['a3'].to_list()\n", "# a3_df = a3_load_and_preprocess(a3_path, retailer_filter=selected_retailers)\n", "\n", "# # read NIQ data\n", "# selected_retailers = retailers['niq'].to_list()\n", "# niq_sku, niq_beer = niq_load_and_preprocess('/dbfs/FileStore/RevMan/NewData.csv', retailer_filter=selected_retailers )"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A3 shape after filtering: (18935, 47)\n"]}], "source": ["# read A3 data\n", "a3_path = \"a3_distribution_march_2025_peter_wf.xlsx\"\n", "selected_retailers = retailers['a3'].to_list()\n", "a3_df = a3_load_and_preprocess(a3_path, retailer_filter=selected_retailers)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:112: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:112: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:112: FutureWarning: The provided callable <function max at 0x000001F2453243A0> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"max\" instead.\n", "  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:144: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:144: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:144: FutureWarning: The provided callable <function max at 0x000001F2453243A0> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"max\" instead.\n", "  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:170: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:170: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\477626786.py:170: FutureWarning: The provided callable <function max at 0x000001F2453243A0> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"max\" instead.\n", "  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({\n"]}], "source": ["# read NIQ data\n", "selected_retailers = retailers['niq'].to_list()\n", "niq_sku, niq_beer, beer_pole = niq_load_and_preprocess('latest_niq_extract.xlsx', retailer_filter=selected_retailers)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Segment distribution:\n", "segment\n", "PREMIUM SUPER PREMIUM LAGERS    10\n", "ABBEY BEER STYLES                8\n", "CORE LAGERS                      3\n", "FLAVOURED                        2\n", "Name: count, dtype: int64\n", "\n", "Beer pole data: 8356 records\n", "Segments in beer pole: 4\n", "\n", "First few beer pole records:\n", "  Market Description date_week_start date_week_end  \\\n", "0       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "1       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "2       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "3       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "4       AUCHAN DRIVE      2022-01-03    2022-01-09   \n", "\n", "                        segment  segment_volume_hl  segment_value  \n", "0             ABBEY BEER STYLES         446.608270  117118.416284  \n", "1                   CORE LAGERS          65.409692   12808.020136  \n", "2                     FLAVOURED         125.748200   43360.152000  \n", "3  PREMIUM SUPER PREMIUM LAGERS         225.173350   50658.241944  \n", "4             ABBEY BEER STYLES         322.837824   89904.235252  \n"]}], "source": ["# Verify mappings were created\n", "print(\"Segment distribution:\")\n", "print(sku_segment_mapping['segment'].value_counts())\n", "print(f\"\\nBeer pole data: {len(beer_pole_renamed)} records\")\n", "print(f\"Segments in beer pole: {beer_pole_renamed['segment'].nunique()}\")\n", "print(\"\\nFirst few beer pole records:\")\n", "print(beer_pole_renamed.head())"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Market Description</th>\n", "      <th>date_week_start</th>\n", "      <th>date_week_end</th>\n", "      <th>sku</th>\n", "      <th>Promo_Value</th>\n", "      <th>Non_Promo_Price</th>\n", "      <th>Promo_Price</th>\n", "      <th>Non_Promo_Value</th>\n", "      <th>Promo_Volume</th>\n", "      <th>Non_Promo_Volume</th>\n", "      <th>...</th>\n", "      <th>Non_Promo_Price_PPC</th>\n", "      <th>Promo_Price_PPC</th>\n", "      <th>volume_hl</th>\n", "      <th>promo_volume_hl</th>\n", "      <th>non_promo_volume_hl</th>\n", "      <th>base_volume_hl</th>\n", "      <th>value_eur</th>\n", "      <th>w_dist</th>\n", "      <th>num_dist</th>\n", "      <th>ros_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>172.478000</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>1267.942000</td>\n", "      <td>94.067000</td>\n", "      <td>526.933000</td>\n", "      <td>...</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>6.210000</td>\n", "      <td>0.940670</td>\n", "      <td>5.269330</td>\n", "      <td>3.065520</td>\n", "      <td>1440.4200</td>\n", "      <td>79.35</td>\n", "      <td>57.25</td>\n", "      <td>13.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>5611.888267</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>7025.646733</td>\n", "      <td>3894.587931</td>\n", "      <td>3465.409069</td>\n", "      <td>...</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>73.599970</td>\n", "      <td>38.945879</td>\n", "      <td>34.654091</td>\n", "      <td>29.549249</td>\n", "      <td>12637.5350</td>\n", "      <td>90.54</td>\n", "      <td>70.99</td>\n", "      <td>155.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 1xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>167.220000</td>\n", "      <td>0.000000</td>\n", "      <td>67.000000</td>\n", "      <td>...</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>0.670000</td>\n", "      <td>0.000000</td>\n", "      <td>0.670000</td>\n", "      <td>0.512330</td>\n", "      <td>167.2200</td>\n", "      <td>11.49</td>\n", "      <td>6.11</td>\n", "      <td>11.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 4xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>773.330000</td>\n", "      <td>0.000000</td>\n", "      <td>312.000000</td>\n", "      <td>...</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>3.120000</td>\n", "      <td>0.000000</td>\n", "      <td>3.120000</td>\n", "      <td>1.859670</td>\n", "      <td>773.3300</td>\n", "      <td>36.48</td>\n", "      <td>25.19</td>\n", "      <td>16.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>AFFLIGEM BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>2419.891000</td>\n", "      <td>0.000000</td>\n", "      <td>884.999000</td>\n", "      <td>...</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>8.849990</td>\n", "      <td>0.000000</td>\n", "      <td>8.849990</td>\n", "      <td>7.792680</td>\n", "      <td>2419.8910</td>\n", "      <td>89.81</td>\n", "      <td>65.65</td>\n", "      <td>20.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39768</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>HEINEKEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>723.089600</td>\n", "      <td>2.895253</td>\n", "      <td>2.605728</td>\n", "      <td>267.810900</td>\n", "      <td>277.500000</td>\n", "      <td>92.500000</td>\n", "      <td>...</td>\n", "      <td>2.895253</td>\n", "      <td>2.605728</td>\n", "      <td>3.700000</td>\n", "      <td>2.775000</td>\n", "      <td>0.925000</td>\n", "      <td>8.514440</td>\n", "      <td>990.9005</td>\n", "      <td>13.97</td>\n", "      <td>12.93</td>\n", "      <td>22.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39769</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>KRONENBOURG OMBRELLE BOTTLE 30xX250ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.237986</td>\n", "      <td>NaN</td>\n", "      <td>2433.809800</td>\n", "      <td>0.000000</td>\n", "      <td>1087.500000</td>\n", "      <td>...</td>\n", "      <td>2.237986</td>\n", "      <td>NaN</td>\n", "      <td>10.875000</td>\n", "      <td>0.000000</td>\n", "      <td>10.875000</td>\n", "      <td>4.920441</td>\n", "      <td>2433.8098</td>\n", "      <td>39.02</td>\n", "      <td>29.65</td>\n", "      <td>19.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39770</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>LA GOUDALE BLONDE BOTTLE 1xX750ML</td>\n", "      <td>2355.724300</td>\n", "      <td>4.347689</td>\n", "      <td>4.368748</td>\n", "      <td>12107.333300</td>\n", "      <td>539.221800</td>\n", "      <td>2784.774200</td>\n", "      <td>...</td>\n", "      <td>4.347689</td>\n", "      <td>4.368748</td>\n", "      <td>33.239960</td>\n", "      <td>5.392218</td>\n", "      <td>27.847742</td>\n", "      <td>31.664727</td>\n", "      <td>14463.0576</td>\n", "      <td>97.66</td>\n", "      <td>89.27</td>\n", "      <td>46.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39771</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>LEFFE BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>653.502900</td>\n", "      <td>4.133193</td>\n", "      <td>3.831776</td>\n", "      <td>7875.595600</td>\n", "      <td>170.548300</td>\n", "      <td>1905.450800</td>\n", "      <td>...</td>\n", "      <td>4.133193</td>\n", "      <td>3.831776</td>\n", "      <td>20.759991</td>\n", "      <td>1.705483</td>\n", "      <td>19.054508</td>\n", "      <td>20.394536</td>\n", "      <td>8529.0985</td>\n", "      <td>87.62</td>\n", "      <td>74.45</td>\n", "      <td>30.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39772</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>LEFFE BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>134.838100</td>\n", "      <td>3.462847</td>\n", "      <td>3.124275</td>\n", "      <td>6049.042100</td>\n", "      <td>43.158200</td>\n", "      <td>1746.840800</td>\n", "      <td>...</td>\n", "      <td>3.462847</td>\n", "      <td>3.124275</td>\n", "      <td>17.899990</td>\n", "      <td>0.431582</td>\n", "      <td>17.468408</td>\n", "      <td>9.757548</td>\n", "      <td>6183.8802</td>\n", "      <td>69.05</td>\n", "      <td>53.94</td>\n", "      <td>28.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>39773 rows × 22 columns</p>\n", "</div>"], "text/plain": ["      Market Description date_week_start date_week_end  \\\n", "0           AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "1           AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "2           AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "3           AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "4           AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "...                  ...             ...           ...   \n", "39768           MONOPRIX      2025-05-19    2025-05-25   \n", "39769           MONOPRIX      2025-05-19    2025-05-25   \n", "39770           MONOPRIX      2025-05-19    2025-05-25   \n", "39771           MONOPRIX      2025-05-19    2025-05-25   \n", "39772           MONOPRIX      2025-05-19    2025-05-25   \n", "\n", "                                         sku  Promo_Value  Non_Promo_Price  \\\n", "0           1664 BLONDE BOTTLE (12-15)X250ML   172.478000         2.406268   \n", "1           1664 BLONDE BOTTLE (20-24)X250ML  5611.888267         2.329011   \n", "2                  8.6 ORIGINAL CAN 1xX500ML     0.000000         2.495821   \n", "3                  8.6 ORIGINAL CAN 4xX500ML     0.000000         2.478622   \n", "4       AFFLIGEM BLONDE BOTTLE (12-15)X250ML     0.000000         2.734343   \n", "...                                      ...          ...              ...   \n", "39768   HEINEKEN BLONDE BOTTLE (20-24)X250ML   723.089600         2.895253   \n", "39769  KRONENBOURG OMBRELLE BOTTLE 30xX250ML     0.000000         2.237986   \n", "39770      LA GOUDALE BLONDE BOTTLE 1xX750ML  2355.724300         4.347689   \n", "39771      LEFFE BLONDE BOTTLE (12-15)X250ML   653.502900         4.133193   \n", "39772      LEFFE BLONDE BOTTLE (20-24)X250ML   134.838100         3.462847   \n", "\n", "       Promo_Price  Non_Promo_Value  Promo_Volume  Non_Promo_Volume  ...  \\\n", "0         1.833565      1267.942000     94.067000        526.933000  ...   \n", "1         1.542802      7025.646733   3894.587931       3465.409069  ...   \n", "2              NaN       167.220000      0.000000         67.000000  ...   \n", "3              NaN       773.330000      0.000000        312.000000  ...   \n", "4              NaN      2419.891000      0.000000        884.999000  ...   \n", "...            ...              ...           ...               ...  ...   \n", "39768     2.605728       267.810900    277.500000         92.500000  ...   \n", "39769          NaN      2433.809800      0.000000       1087.500000  ...   \n", "39770     4.368748     12107.333300    539.221800       2784.774200  ...   \n", "39771     3.831776      7875.595600    170.548300       1905.450800  ...   \n", "39772     3.124275      6049.042100     43.158200       1746.840800  ...   \n", "\n", "       Non_Promo_Price_PPC  Promo_Price_PPC  volume_hl  promo_volume_hl  \\\n", "0                 2.406268         1.833565   6.210000         0.940670   \n", "1                 2.329011         1.542802  73.599970        38.945879   \n", "2                 2.495821              NaN   0.670000         0.000000   \n", "3                 2.478622              NaN   3.120000         0.000000   \n", "4                 2.734343              NaN   8.849990         0.000000   \n", "...                    ...              ...        ...              ...   \n", "39768             2.895253         2.605728   3.700000         2.775000   \n", "39769             2.237986              NaN  10.875000         0.000000   \n", "39770             4.347689         4.368748  33.239960         5.392218   \n", "39771             4.133193         3.831776  20.759991         1.705483   \n", "39772             3.462847         3.124275  17.899990         0.431582   \n", "\n", "       non_promo_volume_hl  base_volume_hl   value_eur  w_dist  num_dist  \\\n", "0                 5.269330        3.065520   1440.4200   79.35     57.25   \n", "1                34.654091       29.549249  12637.5350   90.54     70.99   \n", "2                 0.670000        0.512330    167.2200   11.49      6.11   \n", "3                 3.120000        1.859670    773.3300   36.48     25.19   \n", "4                 8.849990        7.792680   2419.8910   89.81     65.65   \n", "...                    ...             ...         ...     ...       ...   \n", "39768             0.925000        8.514440    990.9005   13.97     12.93   \n", "39769            10.875000        4.920441   2433.8098   39.02     29.65   \n", "39770            27.847742       31.664727  14463.0576   97.66     89.27   \n", "39771            19.054508       20.394536   8529.0985   87.62     74.45   \n", "39772            17.468408        9.757548   6183.8802   69.05     53.94   \n", "\n", "       ros_value  \n", "0          13.86  \n", "1         155.40  \n", "2          11.11  \n", "3          16.18  \n", "4          20.57  \n", "...          ...  \n", "39768      22.38  \n", "39769      19.68  \n", "39770      46.72  \n", "39771      30.71  \n", "39772      28.25  \n", "\n", "[39773 rows x 22 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(niq_sku.head())"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Market Description</th>\n", "      <th>date_week_start</th>\n", "      <th>date_week_end</th>\n", "      <th>sku</th>\n", "      <th>Promo_Value</th>\n", "      <th>Non_Promo_Price</th>\n", "      <th>Promo_Price</th>\n", "      <th>Non_Promo_Value</th>\n", "      <th>volume_hl</th>\n", "      <th>value_eur</th>\n", "      <th>promo_volume_hl</th>\n", "      <th>non_promo_volume_hl</th>\n", "      <th>base_volume_hl</th>\n", "      <th>base_value_PPC</th>\n", "      <th>non_promo_price_PPC</th>\n", "      <th>promo_price_PPC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>71584.312321</td>\n", "      <td>2113.502238</td>\n", "      <td>3.717855e+02</td>\n", "      <td>252063.619690</td>\n", "      <td>1124.844678</td>\n", "      <td>3.236479e+05</td>\n", "      <td>308.437561</td>\n", "      <td>816.407117</td>\n", "      <td>708.797002</td>\n", "      <td>203936.173474</td>\n", "      <td>2113.502136</td>\n", "      <td>3.717431e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2022-01-03</td>\n", "      <td>2022-01-09</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>63167.522783</td>\n", "      <td>1924.363042</td>\n", "      <td>3.319284e+02</td>\n", "      <td>207410.614711</td>\n", "      <td>949.716659</td>\n", "      <td>2.705781e+05</td>\n", "      <td>259.321555</td>\n", "      <td>690.395104</td>\n", "      <td>612.116312</td>\n", "      <td>174390.802617</td>\n", "      <td>1926.049015</td>\n", "      <td>3.319841e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2022-01-10</td>\n", "      <td>2022-01-16</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>35826.168145</td>\n", "      <td>2059.335049</td>\n", "      <td>3.535652e+02</td>\n", "      <td>237545.985015</td>\n", "      <td>928.799155</td>\n", "      <td>2.733722e+05</td>\n", "      <td>146.229266</td>\n", "      <td>766.907555</td>\n", "      <td>651.722944</td>\n", "      <td>191818.610930</td>\n", "      <td>2059.335132</td>\n", "      <td>3.535652e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2022-01-17</td>\n", "      <td>2022-01-23</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>50290.129228</td>\n", "      <td>2014.858778</td>\n", "      <td>2.748719e+08</td>\n", "      <td>244469.228838</td>\n", "      <td>1007.503007</td>\n", "      <td>2.947594e+05</td>\n", "      <td>213.131535</td>\n", "      <td>794.371471</td>\n", "      <td>709.554542</td>\n", "      <td>207588.820757</td>\n", "      <td>2014.794211</td>\n", "      <td>3.665040e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2022-01-24</td>\n", "      <td>2022-01-30</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>50050.122005</td>\n", "      <td>2041.996410</td>\n", "      <td>-5.497370e+07</td>\n", "      <td>255081.091580</td>\n", "      <td>1059.268481</td>\n", "      <td>3.051312e+05</td>\n", "      <td>252.441000</td>\n", "      <td>806.827481</td>\n", "      <td>724.104830</td>\n", "      <td>208584.061815</td>\n", "      <td>2042.473124</td>\n", "      <td>-5.497202e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2084</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-04-21</td>\n", "      <td>2025-04-27</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>184330.977800</td>\n", "      <td>4919.898556</td>\n", "      <td>1.314855e+03</td>\n", "      <td>824708.062000</td>\n", "      <td>2668.633202</td>\n", "      <td>1.009039e+06</td>\n", "      <td>510.389005</td>\n", "      <td>2158.244197</td>\n", "      <td>2202.636403</td>\n", "      <td>832840.615700</td>\n", "      <td>4919.898556</td>\n", "      <td>1.314855e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2085</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-04-28</td>\n", "      <td>2025-05-04</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>216057.408100</td>\n", "      <td>4775.774170</td>\n", "      <td>1.566705e+03</td>\n", "      <td>909224.455500</td>\n", "      <td>2942.928083</td>\n", "      <td>1.125282e+06</td>\n", "      <td>570.506176</td>\n", "      <td>2372.421907</td>\n", "      <td>2542.853691</td>\n", "      <td>972306.170300</td>\n", "      <td>4775.774170</td>\n", "      <td>1.566705e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2086</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-05</td>\n", "      <td>2025-05-11</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>347081.470200</td>\n", "      <td>4814.107418</td>\n", "      <td>1.482864e+03</td>\n", "      <td>763657.024100</td>\n", "      <td>3017.296259</td>\n", "      <td>1.110738e+06</td>\n", "      <td>1059.718178</td>\n", "      <td>1957.578081</td>\n", "      <td>2287.274453</td>\n", "      <td>842000.110000</td>\n", "      <td>4814.107418</td>\n", "      <td>1.482864e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2087</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-12</td>\n", "      <td>2025-05-18</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>336227.981700</td>\n", "      <td>4555.644169</td>\n", "      <td>1.577657e+03</td>\n", "      <td>760649.490300</td>\n", "      <td>2972.454711</td>\n", "      <td>1.096877e+06</td>\n", "      <td>1053.441094</td>\n", "      <td>1919.013617</td>\n", "      <td>2392.274632</td>\n", "      <td>882782.886800</td>\n", "      <td>4555.644169</td>\n", "      <td>1.577657e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2088</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>TOTAL BEER</td>\n", "      <td>237964.862100</td>\n", "      <td>4799.504551</td>\n", "      <td>1.476108e+03</td>\n", "      <td>820074.344800</td>\n", "      <td>2771.217669</td>\n", "      <td>1.058039e+06</td>\n", "      <td>675.801294</td>\n", "      <td>2095.416375</td>\n", "      <td>2206.288759</td>\n", "      <td>842351.734000</td>\n", "      <td>4799.504551</td>\n", "      <td>1.476108e+03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2089 rows × 16 columns</p>\n", "</div>"], "text/plain": ["     Market Description date_week_start date_week_end         sku  \\\n", "0          AUCHAN DRIVE      2021-12-27    2022-01-02  TOTAL BEER   \n", "1          AUCHAN DRIVE      2022-01-03    2022-01-09  TOTAL BEER   \n", "2          AUCHAN DRIVE      2022-01-10    2022-01-16  TOTAL BEER   \n", "3          AUCHAN DRIVE      2022-01-17    2022-01-23  TOTAL BEER   \n", "4          AUCHAN DRIVE      2022-01-24    2022-01-30  TOTAL BEER   \n", "...                 ...             ...           ...         ...   \n", "2084           MONOPRIX      2025-04-21    2025-04-27  TOTAL BEER   \n", "2085           MONOPRIX      2025-04-28    2025-05-04  TOTAL BEER   \n", "2086           MONOPRIX      2025-05-05    2025-05-11  TOTAL BEER   \n", "2087           MONOPRIX      2025-05-12    2025-05-18  TOTAL BEER   \n", "2088           MONOPRIX      2025-05-19    2025-05-25  TOTAL BEER   \n", "\n", "        Promo_Value  Non_Promo_Price   Promo_Price  Non_Promo_Value  \\\n", "0      71584.312321      2113.502238  3.717855e+02    252063.619690   \n", "1      63167.522783      1924.363042  3.319284e+02    207410.614711   \n", "2      35826.168145      2059.335049  3.535652e+02    237545.985015   \n", "3      50290.129228      2014.858778  2.748719e+08    244469.228838   \n", "4      50050.122005      2041.996410 -5.497370e+07    255081.091580   \n", "...             ...              ...           ...              ...   \n", "2084  184330.977800      4919.898556  1.314855e+03    824708.062000   \n", "2085  216057.408100      4775.774170  1.566705e+03    909224.455500   \n", "2086  347081.470200      4814.107418  1.482864e+03    763657.024100   \n", "2087  336227.981700      4555.644169  1.577657e+03    760649.490300   \n", "2088  237964.862100      4799.504551  1.476108e+03    820074.344800   \n", "\n", "        volume_hl     value_eur  promo_volume_hl  non_promo_volume_hl  \\\n", "0     1124.844678  3.236479e+05       308.437561           816.407117   \n", "1      949.716659  2.705781e+05       259.321555           690.395104   \n", "2      928.799155  2.733722e+05       146.229266           766.907555   \n", "3     1007.503007  2.947594e+05       213.131535           794.371471   \n", "4     1059.268481  3.051312e+05       252.441000           806.827481   \n", "...           ...           ...              ...                  ...   \n", "2084  2668.633202  1.009039e+06       510.389005          2158.244197   \n", "2085  2942.928083  1.125282e+06       570.506176          2372.421907   \n", "2086  3017.296259  1.110738e+06      1059.718178          1957.578081   \n", "2087  2972.454711  1.096877e+06      1053.441094          1919.013617   \n", "2088  2771.217669  1.058039e+06       675.801294          2095.416375   \n", "\n", "      base_volume_hl  base_value_PPC  non_promo_price_PPC  promo_price_PPC  \n", "0         708.797002   203936.173474          2113.502136     3.717431e+02  \n", "1         612.116312   174390.802617          1926.049015     3.319841e+02  \n", "2         651.722944   191818.610930          2059.335132     3.535652e+02  \n", "3         709.554542   207588.820757          2014.794211     3.665040e+08  \n", "4         724.104830   208584.061815          2042.473124    -5.497202e+07  \n", "...              ...             ...                  ...              ...  \n", "2084     2202.636403   832840.615700          4919.898556     1.314855e+03  \n", "2085     2542.853691   972306.170300          4775.774170     1.566705e+03  \n", "2086     2287.274453   842000.110000          4814.107418     1.482864e+03  \n", "2087     2392.274632   882782.886800          4555.644169     1.577657e+03  \n", "2088     2206.288759   842351.734000          4799.504551     1.476108e+03  \n", "\n", "[2089 rows x 16 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(niq_beer)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Market Description</th>\n", "      <th>date_week_start</th>\n", "      <th>date_week_end</th>\n", "      <th>sku</th>\n", "      <th>Promo_Value</th>\n", "      <th>Non_Promo_Price</th>\n", "      <th>Promo_Price</th>\n", "      <th>Non_Promo_Value</th>\n", "      <th>volume_hl</th>\n", "      <th>value_eur</th>\n", "      <th>promo_volume_hl</th>\n", "      <th>non_promo_volume_hl</th>\n", "      <th>base_volume_hl</th>\n", "      <th>base_value_PPC</th>\n", "      <th>non_promo_price_PPC</th>\n", "      <th>promo_price_PPC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>ABBEY BEER STYLES</td>\n", "      <td>41415.299810</td>\n", "      <td>379.246900</td>\n", "      <td>123.020781</td>\n", "      <td>75703.116474</td>\n", "      <td>446.608270</td>\n", "      <td>117118.416284</td>\n", "      <td>190.325932</td>\n", "      <td>256.282338</td>\n", "      <td>235.415368</td>\n", "      <td>67465.309858</td>\n", "      <td>379.247132</td>\n", "      <td>122.978995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>CORE LAGERS</td>\n", "      <td>512.871981</td>\n", "      <td>81.356472</td>\n", "      <td>7.777819</td>\n", "      <td>12295.148154</td>\n", "      <td>65.409692</td>\n", "      <td>12808.020136</td>\n", "      <td>3.071940</td>\n", "      <td>62.337752</td>\n", "      <td>56.405602</td>\n", "      <td>10740.758888</td>\n", "      <td>81.356472</td>\n", "      <td>7.777820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>FLAVOURED</td>\n", "      <td>5195.293000</td>\n", "      <td>104.356904</td>\n", "      <td>9.592864</td>\n", "      <td>38164.859000</td>\n", "      <td>125.748200</td>\n", "      <td>43360.152000</td>\n", "      <td>18.051330</td>\n", "      <td>107.696870</td>\n", "      <td>91.065717</td>\n", "      <td>31278.675110</td>\n", "      <td>104.356935</td>\n", "      <td>9.592864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>PREMIUM SUPER PREMIUM LAGERS</td>\n", "      <td>6893.555711</td>\n", "      <td>108.966877</td>\n", "      <td>28.913603</td>\n", "      <td>43764.686233</td>\n", "      <td>225.173350</td>\n", "      <td>50658.241944</td>\n", "      <td>44.515218</td>\n", "      <td>180.658132</td>\n", "      <td>149.282904</td>\n", "      <td>35861.403378</td>\n", "      <td>108.966813</td>\n", "      <td>28.913606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2022-01-03</td>\n", "      <td>2022-01-09</td>\n", "      <td>ABBEY BEER STYLES</td>\n", "      <td>18188.676851</td>\n", "      <td>383.776180</td>\n", "      <td>98.531086</td>\n", "      <td>71715.558400</td>\n", "      <td>322.837824</td>\n", "      <td>89904.235252</td>\n", "      <td>79.601934</td>\n", "      <td>243.235890</td>\n", "      <td>210.700295</td>\n", "      <td>60848.305898</td>\n", "      <td>383.776107</td>\n", "      <td>98.595800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8351</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-12</td>\n", "      <td>2025-05-18</td>\n", "      <td>PREMIUM SUPER PREMIUM LAGERS</td>\n", "      <td>158480.741400</td>\n", "      <td>307.962174</td>\n", "      <td>117.158520</td>\n", "      <td>218321.434800</td>\n", "      <td>1285.795392</td>\n", "      <td>376802.176200</td>\n", "      <td>608.810556</td>\n", "      <td>676.984836</td>\n", "      <td>1023.196932</td>\n", "      <td>302127.353100</td>\n", "      <td>307.962174</td>\n", "      <td>117.158520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8352</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>ABBEY BEER STYLES</td>\n", "      <td>36435.542300</td>\n", "      <td>278.550256</td>\n", "      <td>102.981857</td>\n", "      <td>161262.623900</td>\n", "      <td>499.792795</td>\n", "      <td>197698.166200</td>\n", "      <td>101.026397</td>\n", "      <td>398.766398</td>\n", "      <td>416.028531</td>\n", "      <td>167128.768200</td>\n", "      <td>278.550256</td>\n", "      <td>102.981857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8353</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>CORE LAGERS</td>\n", "      <td>11520.601900</td>\n", "      <td>61.300358</td>\n", "      <td>30.722934</td>\n", "      <td>93869.311500</td>\n", "      <td>368.832287</td>\n", "      <td>105389.913400</td>\n", "      <td>40.869024</td>\n", "      <td>327.963263</td>\n", "      <td>336.071600</td>\n", "      <td>96151.677900</td>\n", "      <td>61.300358</td>\n", "      <td>30.722934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8354</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>FLAVOURED</td>\n", "      <td>2585.755200</td>\n", "      <td>128.970070</td>\n", "      <td>13.684751</td>\n", "      <td>29783.193300</td>\n", "      <td>68.476384</td>\n", "      <td>32368.948500</td>\n", "      <td>5.736647</td>\n", "      <td>62.739737</td>\n", "      <td>68.496297</td>\n", "      <td>31781.222000</td>\n", "      <td>128.970070</td>\n", "      <td>13.684751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8355</th>\n", "      <td>MONOPRIX</td>\n", "      <td>2025-05-19</td>\n", "      <td>2025-05-25</td>\n", "      <td>PREMIUM SUPER PREMIUM LAGERS</td>\n", "      <td>115611.037600</td>\n", "      <td>341.591014</td>\n", "      <td>123.186689</td>\n", "      <td>244484.434300</td>\n", "      <td>1156.239037</td>\n", "      <td>360095.471900</td>\n", "      <td>389.258608</td>\n", "      <td>766.980429</td>\n", "      <td>867.388001</td>\n", "      <td>269845.335000</td>\n", "      <td>341.591014</td>\n", "      <td>123.186689</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8356 rows × 16 columns</p>\n", "</div>"], "text/plain": ["     Market Description date_week_start date_week_end  \\\n", "0          AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "1          AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "2          AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "3          AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "4          AUCHAN DRIVE      2022-01-03    2022-01-09   \n", "...                 ...             ...           ...   \n", "8351           MONOPRIX      2025-05-12    2025-05-18   \n", "8352           MONOPRIX      2025-05-19    2025-05-25   \n", "8353           MONOPRIX      2025-05-19    2025-05-25   \n", "8354           MONOPRIX      2025-05-19    2025-05-25   \n", "8355           MONOPRIX      2025-05-19    2025-05-25   \n", "\n", "                               sku    Promo_Value  Non_Promo_Price  \\\n", "0                ABBEY BEER STYLES   41415.299810       379.246900   \n", "1                      CORE LAGERS     512.871981        81.356472   \n", "2                        FLAVOURED    5195.293000       104.356904   \n", "3     PREMIUM SUPER PREMIUM LAGERS    6893.555711       108.966877   \n", "4                ABBEY BEER STYLES   18188.676851       383.776180   \n", "...                            ...            ...              ...   \n", "8351  PREMIUM SUPER PREMIUM LAGERS  158480.741400       307.962174   \n", "8352             ABBEY BEER STYLES   36435.542300       278.550256   \n", "8353                   CORE LAGERS   11520.601900        61.300358   \n", "8354                     FLAVOURED    2585.755200       128.970070   \n", "8355  PREMIUM SUPER PREMIUM LAGERS  115611.037600       341.591014   \n", "\n", "      Promo_Price  Non_Promo_Value    volume_hl      value_eur  \\\n", "0      123.020781     75703.116474   446.608270  117118.416284   \n", "1        7.777819     12295.148154    65.409692   12808.020136   \n", "2        9.592864     38164.859000   125.748200   43360.152000   \n", "3       28.913603     43764.686233   225.173350   50658.241944   \n", "4       98.531086     71715.558400   322.837824   89904.235252   \n", "...           ...              ...          ...            ...   \n", "8351   117.158520    218321.434800  1285.795392  376802.176200   \n", "8352   102.981857    161262.623900   499.792795  197698.166200   \n", "8353    30.722934     93869.311500   368.832287  105389.913400   \n", "8354    13.684751     29783.193300    68.476384   32368.948500   \n", "8355   123.186689    244484.434300  1156.239037  360095.471900   \n", "\n", "      promo_volume_hl  non_promo_volume_hl  base_volume_hl  base_value_PPC  \\\n", "0          190.325932           256.282338      235.415368    67465.309858   \n", "1            3.071940            62.337752       56.405602    10740.758888   \n", "2           18.051330           107.696870       91.065717    31278.675110   \n", "3           44.515218           180.658132      149.282904    35861.403378   \n", "4           79.601934           243.235890      210.700295    60848.305898   \n", "...               ...                  ...             ...             ...   \n", "8351       608.810556           676.984836     1023.196932   302127.353100   \n", "8352       101.026397           398.766398      416.028531   167128.768200   \n", "8353        40.869024           327.963263      336.071600    96151.677900   \n", "8354         5.736647            62.739737       68.496297    31781.222000   \n", "8355       389.258608           766.980429      867.388001   269845.335000   \n", "\n", "      non_promo_price_PPC  promo_price_PPC  \n", "0              379.247132       122.978995  \n", "1               81.356472         7.777820  \n", "2              104.356935         9.592864  \n", "3              108.966813        28.913606  \n", "4              383.776107        98.595800  \n", "...                   ...              ...  \n", "8351           307.962174       117.158520  \n", "8352           278.550256       102.981857  \n", "8353            61.300358        30.722934  \n", "8354           128.970070        13.684751  \n", "8355           341.591014       123.186689  \n", "\n", "[8356 rows x 16 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(beer_pole)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c7c32740-022d-47ed-9bfe-f74f59c6bf03", "showTitle": true, "tableResultSettingsMap": {}, "title": "Define dataframe at promo-level"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18935\n", "total num unique promotions 18643\n", "unique number of promotions with attributes 18643\n"]}], "source": ["# define promo ID\n", "a3_df['promo_id'] = a3_df['Enseigne'] + '_' + a3_df['A3 Distribution SKU'] + '_'+ a3_df['Date de début'].dt.strftime('%Y-%m-%d') + '_'+ a3_df['Date de fin'].dt.strftime('%Y-%m-%d')\n", "\n", "print(len(a3_df))\n", "print('total num unique promotions', a3_df['promo_id'].nunique())\n", "\n", "temp = a3_df.groupby(['promo_id','Similar_SKU_Mapping' ], as_index=False)['Enseigne'].count().sort_values(by='Enseigne',ascending=False)\n", "# a3_df.loc[a3_df['promo_id'].isin(temp.loc[(temp['Enseigne'] > 1), 'promo_id'].unique())].to_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/duplicate_promoids.csv')\n", "\n", "# define promo granularity\n", "promo = a3_df.groupby(['promo_id', 'Similar_SKU_Mapping', 'Enseigne', 'Date de début', 'Date de fin'], as_index=False, dropna=False).agg({\n", "    'Rounded': 'max',\n", "    'Bucket': 'first',\n", "    'MECHA': 'first',\n", "    'Dn OP': 'max'\n", "})\n", "\n", "print('unique number of promotions with attributes', len(promo['promo_id']))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b4f1d985-d24b-4175-b07b-b3c80ab7e68b", "showTitle": true, "tableResultSettingsMap": {}, "title": "Calculate relative promo timings"}}, "outputs": [], "source": ["# import pandas as pd\n", "# import numpy as np\n", "\n", "# # Ensure date columns are in datetime format\n", "# promo['Date de début'] = pd.to_datetime(promo['Date de début'], errors='coerce')\n", "# promo['Date de fin'] = pd.to_datetime(promo['Date de fin'], errors='coerce')\n", "\n", "# # Filter out rows where dates could not be parsed\n", "# promo.dropna(subset=['Date de début', 'Date de fin'], inplace=True)\n", "\n", "# abi_promos = promo[promo['Similar_SKU_Mapping'].isin(similar_skus['abi_sku'])]\n", "# comp_promos = promo[promo['Similar_SKU_Mapping'].isin(similar_skus['comp_sku'])]\n", "\n", "# records_wo_interaction = []\n", "# records = []\n", "\n", "# # Match promos per enseigne\n", "# for retailer in promo['Enseigne'].unique():\n", "#   for abi_sku in similar_skus['abi_sku'].unique():\n", "#     # filter ABI promos only for selected SKU\n", "#     filt_abi_promos = abi_promos[(abi_promos['Enseigne'] == retailer ) & (abi_promos['Similar_SKU_Mapping'] == abi_sku)].copy()\n", "    \n", "#     # skip if no ABI promos\n", "#     if len(filt_abi_promos) == 0:\n", "#       continue\n", "\n", "#     for comp_sku in similar_skus.loc[similar_skus['abi_sku'] == abi_sku, 'comp_sku']:\n", "#         # filter Comp promos only for selected SKU\n", "#         filt_comp_promos = comp_promos[(comp_promos['Enseigne'] == retailer ) & (comp_promos['Similar_SKU_Mapping'] == comp_sku)].copy()\n", "        \n", "#         # skip if no Comp promos\n", "#         if len(filt_comp_promos) == 0:\n", "#           continue\n", "\n", "#         # iteratively compare each ABI promo vs. each competitor promo \n", "#         for _, a_row in filt_abi_promos.iterrows():\n", "#           for _, c_row in filt_comp_promos.iterrows(): \n", "            \n", "#             abi_start, abi_end = a_row['Date de début'], a_row['Date de fin']\n", "#             comp_start, comp_end = c_row['Date de début'], c_row['Date de fin']\n", "            \n", "#             # Fixed origin week calculation (Sunday-based like ISO weeks)\n", "#             origin = pd.Timestamp(\"2022-01-02\")  # Sunday of ISO week 1 in 2022\n", "\n", "#             # Calculate week offsets from origin\n", "#             abi_week_val = ((abi_start - origin).days) // 7\n", "#             comp_week_val = ((comp_start - origin).days) // 7\n", "\n", "#             # Calculate timing (ABI relative to competitor) \n", "#             timing_weeks = abi_week_val - comp_week_val\n", "            \n", "#             # Calculate timing (ABI relative to competitor)\n", "#             timing_weeks = abi_week_val - comp_week_val\n", "            \n", "#             # CORRECTED: if promos are more than 4 weeks apart (to capture 3 weeks before/after), treat as no interaction\n", "#             if abs(timing_weeks) > 3:\n", "#               records_wo_interaction.append({\n", "#                 'ABI PromoID': a_row['promo_id'],\n", "#                 'Retailer': retailer,\n", "#                 'ABI SKU': a_row['Similar_SKU_Mapping'],\n", "#                 'ABI Start': abi_start,\n", "#                 'ABI End': abi_end,\n", "#                 'ABI Coverage': a_row['Dn OP'],\n", "#                 'ABI Mechanic': a_row['MECHA'], \n", "#                 'ABI Depth': a_row['Bucket'],\n", "#                 'ABI Rounded': a_row['Rounded'],\n", "#                 'Competitor PromoID': 'No interaction',\n", "#                 'Competitor SKU': '',\n", "#                 'Competitor Start': np.nan,\n", "#                 'Competitor End': np.nan,\n", "#                 'Competitor Coverage': np.nan,\n", "#                 'Competitor Mechanic': np.nan, \n", "#                 'Competitor Depth': np.nan,\n", "#                 'Timing': np.nan,\n", "#                 'Weeks since last comp promo': np.nan,\n", "#                 'Weeks until next comp promo': np.nan,\n", "#                 'Overlap Promos': np.nan,\n", "#                 'Actual Overlap Days': np.nan,\n", "#                 'Category': 'No interaction'\n", "#               }) \n", "#               continue\n", "\n", "#             # Calculate overlap (keep original logic)\n", "#             overlap_days = max(0, (min(abi_end, comp_end) - max(abi_start, comp_start)).days)\n", "#             overlap_flag = 1 if overlap_days >= 7 else 0\n", "\n", "#             # Map ISO week timing to original weeks_since/weeks_until logic\n", "#             weeks_since = 999\n", "#             weeks_until = -999\n", "\n", "#             if overlap_days > 0:\n", "#                 # If overlap, both are 0 (matching original logic)\n", "#                 weeks_since = 0\n", "#                 weeks_until = 0\n", "#             else:\n", "#                 # No overlap - map ISO week timing to original structure\n", "#                 if timing_weeks > 0:\n", "#                     # ABI starts after competitor (positive timing)\n", "#                     weeks_since = timing_weeks  # No need to cap since we already filtered > 4\n", "#                 elif timing_weeks < 0:\n", "#                     # Competitor starts after ABI (negative timing)  \n", "#                     weeks_until = timing_weeks  # No need to cap since we already filtered < -4\n", "\n", "#             # Original category logic\n", "#             if overlap_days >= 1:\n", "#                 category = \"Overlap\"\n", "#             elif weeks_since != 999:\n", "#                 category = \"After\"\n", "#             elif weeks_until != -999:\n", "#                 category = \"Before\"\n", "#             else:\n", "#                 category = \"Unclear\"\n", "\n", "#             # KEEP ORIGINAL BEHAVIOR: Don't skip \"Unclear\" records\n", "#             if category != 'Unclear': \n", "#               records.append({\n", "#                   'ABI PromoID': a_row['promo_id'],\n", "#                   'Retailer': retailer,\n", "#                   'ABI SKU': a_row['Similar_SKU_Mapping'],\n", "#                   'ABI Start': abi_start,\n", "#                   'ABI End': abi_end,\n", "#                   'ABI Coverage': a_row['Dn OP'],\n", "#                   'ABI Mechanic': a_row['MECHA'], \n", "#                   'ABI Depth': a_row['Bucket'],\n", "#                   'ABI Rounded': a_row['Rounded'],\n", "#                   'Competitor PromoID': c_row['promo_id'],\n", "#                   'Competitor SKU': c_row['Similar_SKU_Mapping'],\n", "#                   'Competitor Start': comp_start,\n", "#                   'Competitor End': comp_end,\n", "#                   'Competitor Coverage': c_row['Dn OP'],\n", "#                   'Competitor Mechanic': c_row['MECHA'], \n", "#                   'Competitor Depth': c_row['Bucket'],\n", "#                   'Timing': timing_weeks, \n", "#                   'Weeks since last comp promo': weeks_since,\n", "#                   'Weeks until next comp promo': weeks_until,\n", "#                   'Overlap Promos': overlap_flag,\n", "#                   'Actual Overlap Days': overlap_days,\n", "#                   'Category': category\n", "#               })\n", "\n", "# # Original concatenation logic\n", "# df_w_interaction = pd.DataFrame(records)\n", "# df_wo_interaction = pd.DataFrame(records_wo_interaction).drop_duplicates()\n", "# df_wo_interaction = df_wo_interaction[~df_wo_interaction['ABI PromoID'].isin(df_w_interaction['ABI PromoID'])]\n", "\n", "# final_df = pd.concat([df_w_interaction, df_wo_interaction], axis=0)\n", "\n", "# print(f\"Created {len(final_df)} total records\")\n", "# print(f\"- Interaction records: {len(df_w_interaction)}\")\n", "# print(f\"- No-interaction records: {len(df_wo_interaction)}\")\n", "# print(\"\\nTiming distribution:\")\n", "# print(final_df['Category'].value_counts())\n", "# print(\"\\nISO Week-based timing values:\")\n", "# print(final_df['Timing'].value_counts().sort_index())"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1094 total records\n", "- Interaction records: 1028\n", "- No-interaction records: 66\n", "\n", "Timing distribution:\n", "Category\n", "Overlap           357\n", "Before            347\n", "After             324\n", "No interaction     66\n", "Name: count, dtype: int64\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def calculate_promo_interactions(promo_df, similar_skus_df, max_days_apart=21):\n", "    \"\"\"\n", "    Calculates the timing interaction between ABI and competitor promotions\n", "    using a direct day-based comparison.\n", "    This function identifies overlapping promotions and promotions that occur\n", "    within a specified number of weeks before or after a competitor's promotion.\n", "    \"\"\"\n", "    promo_df = promo_df.copy()\n", "    promo_df['Date de début'] = pd.to_datetime(promo_df['Date de début'], errors='coerce')\n", "    promo_df['Date de fin'] = pd.to_datetime(promo_df['Date de fin'], errors='coerce')\n", "    promo_df.dropna(subset=['Date de début', 'Date de fin'], inplace=True)\n", "\n", "    abi_promos = promo_df[promo_df['Similar_SKU_Mapping'].isin(similar_skus_df['abi_sku'])].copy()\n", "    comp_promos = promo_df[promo_df['Similar_SKU_Mapping'].isin(similar_skus_df['comp_sku'])].copy()\n", "\n", "    common_enseignes = set(abi_promos['Enseigne']) & set(comp_promos['Enseigne'])\n", "    abi_promos = abi_promos[abi_promos['Enseigne'].isin(common_enseignes)]\n", "    comp_promos = comp_promos[comp_promos['Enseigne'].isin(common_enseignes)]\n", "\n", "    records = []\n", "    interacting_abi_promo_ids = set()\n", "\n", "    for enseigne in common_enseignes:\n", "        abi_sub = abi_promos[abi_promos['Enseigne'] == enseigne]\n", "        comp_sub = comp_promos[comp_promos['Enseigne'] == enseigne]\n", "\n", "        for _, a_row in abi_sub.iterrows():\n", "            current_abi_sku = a_row['Similar_SKU_Mapping']\n", "            associated_comp_skus = similar_skus_df[similar_skus_df['abi_sku'] == current_abi_sku]['comp_sku']\n", "            comp_sub_filtered = comp_sub[comp_sub['Similar_SKU_Mapping'].isin(associated_comp_skus)]\n", "\n", "            if comp_sub_filtered.empty:\n", "                continue\n", "\n", "            for _, c_row in comp_sub_filtered.iterrows():\n", "                if abs((a_row['Date de début'] - c_row['Date de début']).days) > max_days_apart:\n", "                    continue\n", "\n", "                abi_start, abi_end = a_row['Date de début'], a_row['Date de fin']\n", "                comp_start, comp_end = c_row['Date de début'], c_row['Date de fin']\n", "\n", "                # Default values for 'no interaction' or 'far apart'\n", "                weeks_since = 999\n", "                weeks_until = -999\n", "                timing_val = np.nan\n", "                category = \"Unclear\"\n", "                \n", "                # First, check for overlap\n", "                overlap_days = max(0, (min(abi_end, comp_end) - max(abi_start, comp_start)).days + 1)\n", "                \n", "                if overlap_days >= 1:\n", "                    category = \"Overlap\"\n", "                    weeks_since = 0\n", "                    weeks_until = 0\n", "                    timing_val = 0\n", "                    overlap_flag = 1\n", "                else: # No overlap, so check for Before/After\n", "                    overlap_flag = 0\n", "                    days_since = (abi_start - comp_end).days\n", "                    days_until = (comp_start - abi_end).days\n", "\n", "                    if 0 < days_since <= max_days_apart:\n", "                        category = \"After\"\n", "                        timing_val = (days_since + 6) // 7  # Convert days to weeks\n", "                        weeks_since = timing_val\n", "                    elif 0 < days_until <= max_days_apart:\n", "                        category = \"Before\"\n", "                        timing_val = -((days_until + 6) // 7) # Convert days to weeks and make negative\n", "                        weeks_until = timing_val\n", "\n", "                if category != \"Unclear\":\n", "                    interacting_abi_promo_ids.add(a_row['promo_id'])\n", "                    records.append({\n", "                        'ABI PromoID': a_row['promo_id'], 'Retailer': enseigne,\n", "                        'ABI SKU': a_row['Similar_SKU_Mapping'], 'ABI Start': abi_start, 'ABI End': abi_end,\n", "                        'ABI Coverage': a_row['Dn OP'], 'ABI Mechanic': a_row['MECHA'],\n", "                        'ABI Depth': a_row['Bucket'], 'ABI Rounded': a_row['Rounded'],\n", "                        'Competitor PromoID': c_row['promo_id'], 'Competitor SKU': c_row['Similar_SKU_Mapping'],\n", "                        'Competitor Start': comp_start, 'Competitor End': comp_end,\n", "                        'Competitor Coverage': c_row['Dn OP'], 'Competitor Mechanic': c_row['MECHA'],\n", "                        'Competitor Depth': c_row['Bucket'], 'Timing': timing_val,\n", "                        'Weeks since last comp promo': weeks_since, 'Weeks until next comp promo': weeks_until,\n", "                        'Overlap Promos': overlap_flag, 'Actual Overlap Days': overlap_days, 'Category': category\n", "                    })\n", "\n", "    all_abi_promo_ids = set(abi_promos['promo_id'])\n", "    no_interaction_ids = all_abi_promo_ids - interacting_abi_promo_ids\n", "    no_interaction_promos = abi_promos[abi_promos['promo_id'].isin(no_interaction_ids)].drop_duplicates(subset=['promo_id'])\n", "    \n", "    records_wo_interaction = [{\n", "        'ABI PromoID': a_row['promo_id'], 'Retailer': a_row['Enseigne'], 'ABI SKU': a_row['Similar_SKU_Mapping'],\n", "        'ABI Start': a_row['Date de début'], 'ABI End': a_row['Date de fin'], 'ABI Coverage': a_row['Dn OP'],\n", "        'ABI Mechanic': a_row['MECHA'], 'ABI Depth': a_row['Bucket'], 'ABI Rounded': a_row['Rounded'],\n", "        'Competitor PromoID': 'No interaction', 'Competitor SKU': '', 'Competitor Start': pd.NaT, 'Competitor End': pd.NaT,\n", "        'Competitor Coverage': np.nan, 'Competitor Mechanic': np.nan, 'Competitor Depth': np.nan,\n", "        'Timing': np.nan, 'Weeks since last comp promo': 999, 'Weeks until next comp promo': -999,\n", "        'Overlap Promos': 0, 'Actual Overlap Days': 0, 'Category': 'No interaction'\n", "    } for _, a_row in no_interaction_promos.iterrows()]\n", "\n", "    if MELT_SKU_FLAG:\n", "        df_w_interaction = pd.DataFrame(records)            #  NO drop_duplicates\n", "    else:\n", "        df_w_interaction = pd.DataFrame(records).drop_duplicates() if records else pd.DataFrame(records)\n", "    df_wo_interaction = pd.DataFrame(records_wo_interaction)\n", "    final_df = pd.concat([df_w_interaction, df_wo_interaction], ignore_index=True, sort=False)\n", "    \n", "    print(f\"Created {len(final_df)} total records\")\n", "    print(f\"- Interaction records: {len(df_w_interaction)}\")\n", "    print(f\"- No-interaction records: {len(df_wo_interaction)}\")\n", "    print(\"\\nTiming distribution:\")\n", "    print(final_df['Category'].value_counts())\n", "    \n", "    return final_df\n", "\n", "# Execute the function to generate the final dataframe\n", "final_df = calculate_promo_interactions(promo, similar_skus, max_days_apart=21)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== UNMELTED MODE AGGREGATION ===\n", "Before aggregation: 1094 rows\n", "Interaction rows to aggregate: 1028\n", "Non-interaction rows (unchanged): 66\n", "Cases where same comp SKU has multiple promos vs same ABI promo: 187\n", "Sample merge cases:\n", "  ABI: AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25 | Comp SKU: 1664 BLONDE BOTTLE (12-15)X250ML | Count: 2\n", "  ABI: AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-11_2022-05-17 | Comp SKU: GRIMBERGEN BLONDE BOTTLE (20-24)X250ML | Count: 2\n", "  ABI: AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-11_2022-05-17 | Comp SKU: HEINEKEN BLONDE BOTTLE (20-24)X250ML | Count: 2\n", "After aggregation: 819 interaction rows\n", "Rows reduced by: 209 (same comp SKU with multiple promos)\n", "Post-collapse total rows: 885\n"]}], "source": ["# ---------- Post-aggregation collapse when flag is OFF ----------\n", "if not MELT_SKU_FLAG:\n", "    print(f'\\n=== UNMELTED MODE AGGREGATION ===')\n", "    print(f'Before aggregation: {len(final_df)} rows')\n", "    interaction_mask = (final_df['Competitor SKU'].notna()) & (final_df['Competitor SKU'] != '')\n", "    int_df = final_df[interaction_mask]\n", "    non_int_df = final_df[~interaction_mask]\n", "    print(f'Interaction rows to aggregate: {len(int_df)}')\n", "    print(f'Non-interaction rows (unchanged): {len(non_int_df)}')\n", "\n", "    # CORRECTED: Keep 'Competitor SKU' in group_cols to preserve different competitor SKUs\n", "    # Only merge when the SAME competitor SKU has multiple promos against the SAME ABI promo\n", "    group_cols = ['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU',\n", "                  'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic',\n", "                  'ABI Depth', 'ABI Rounded']\n", "\n", "    # Check for potential merges BEFORE aggregation\n", "    potential_merges = int_df.groupby(group_cols).size()\n", "    merge_cases = potential_merges[potential_merges > 1]\n", "    print(f'Cases where same comp SKU has multiple promos vs same ABI promo: {len(merge_cases)}')\n", "    if len(merge_cases) > 0:\n", "        print('Sample merge cases:')\n", "        for group_key, count in merge_cases.head(3).items():\n", "            print(f'  ABI: {group_key[1]} | Comp SKU: {group_key[3]} | Count: {count}')\n", "\n", "    agg_dict = {\n", "        'Competitor PromoID': lambda x: '|'.join(sorted(set(x.dropna().astype(str)))),  # Pipe-join multiple promo IDs\n", "        'Competitor Start'  : 'min',  # Earliest start date\n", "        'Competitor End'    : 'max',  # Latest end date\n", "        'Competitor Coverage': 'mean',  # Average coverage\n", "        'Competitor Depth'  : 'first',  # Take first depth value\n", "        'Timing'            : 'first',  # Take first timing category\n", "        'Weeks since last comp promo': 'first',\n", "        'Weeks until next comp promo': 'first',\n", "        'Overlap Promos'    : 'max',   # Maximum overlap count\n", "        'Actual Overlap Days': 'max',  # Maximum overlap days\n", "        'Category'          : 'first'  # Take first category\n", "    }\n", "    \n", "    # Filter agg_dict to only include columns that exist in the data\n", "    available_agg_dict = {}\n", "    for col, func in agg_dict.items():\n", "        if col in int_df.columns:\n", "            available_agg_dict[col] = func\n", "        else:\n", "            print(f'Warning: Column \\'{col}\\' not found in data, skipping')\n", "    \n", "    int_df_aggregated = int_df.groupby(group_cols, as_index=False).agg(available_agg_dict)\n", "    print(f'After aggregation: {len(int_df_aggregated)} interaction rows')\n", "    print(f'Rows reduced by: {len(int_df) - len(int_df_aggregated)} (same comp SKU with multiple promos)')\n", "    final_df = pd.concat([int_df_aggregated, non_int_df], ignore_index=True)\n", "    print(f'Post-collapse total rows: {len(final_df)}')\n", "else:\n", "    print(f'\\n=== MELTED MODE (NO AGGREGATION) ===')\n", "    print(f'Total rows: {len(final_df)} (no aggregation)')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== MELT_SKU_FLAG VERIFICATION ===\n", "MELT_SKU_FLAG setting: False\n", "Final total rows: 885\n", "Unique ABI PromoIDs: 430\n", "Unique ABI SKUs: 5\n", "Unique Competitor SKUs: 13\n", "Rows with competitor interactions: 819\n", "Rows without competitor interactions: 66\n", "\n", "Sample Competitor SKU values (should be individual SKUs):\n", "  1. 1664 BLONDE BOTTLE (12-15)X250ML\n", "  2. GRIMBERGEN BLONDE BOTTLE (20-24)X250ML\n", "  3. GRIMBERGEN BLONDE BOTTLE (20-24)X250ML\n", "  4. HEINEKEN BLONDE BOTTLE (20-24)X250ML\n", "  5. GRIMBERGEN BLONDE BOTTLE (20-24)X250ML\n", "\n", "Rows with pipe-joined Competitor PromoIDs (same comp SKU, multiple promos): 187\n", "Sample merged rows:\n", "  ABI: AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25\n", "  Comp SKU: 1664 BLONDE BOTTLE (12-15)X250ML\n", "  Comp PromoIDs: AUCHAN_1664_1664 BLONDE_BIERE 1664_12 BTL 25 CL_2022-10-05_2022-10-11|AUCHAN_1664_1664 BLONDE_BIERE 1664_12 BTL 25 CL_2022-10-26_2022-11-01\n", "\n", "  ABI: AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-11_2022-05-17\n", "  Comp SKU: GRIMBERGEN BLONDE BOTTLE (20-24)X250ML\n", "  Comp PromoIDs: AUCHAN_GRIMBERGEN_GRIMBERGEN BLONDE_BIERE BLONDE ABBAYE_20 BTL 25 CL_2022-04-27_2022-05-03|AUCHAN_GRIMBERGEN_GRIMBERGEN BLONDE_BIERE BLONDE ABBAYE_24 BTL 25 CL_2022-06-01_2022-06-07\n", "\n", "  ABI: AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-11_2022-05-17\n", "  Comp SKU: HEINEKEN BLONDE BOTTLE (20-24)X250ML\n", "  Comp PromoIDs: AUCHAN_HEINEKEN_HEINEKEN CLASSIQUE_BIERE BLONDE PRESTIGE (ORIGINAL)_20 BTL 25 CL_2022-04-27_2022-05-03|AUCHAN_HEINEKEN_HEINEKEN CLASSIQUE_BIERE BLONDE PRESTIGE (ORIGINAL)_24 BTL 25 CL_2022-05-04_2022-05-10\n", "\n", "==================================================\n"]}], "source": ["# DEBUG: Verify the melting logic is working correctly\n", "print(f'\\n=== MELT_SKU_FLAG VERIFICATION ===')\n", "print(f'MELT_SKU_FLAG setting: {MELT_SKU_FLAG}')\n", "print(f'Final total rows: {len(final_df)}')\n", "print(f'Unique ABI PromoIDs: {final_df[\"ABI PromoID\"].nunique()}')\n", "print(f'Unique ABI SKUs: {final_df[\"ABI SKU\"].nunique()}')\n", "comp_skus = final_df[final_df[\"Competitor SKU\"].notna() & (final_df[\"Competitor SKU\"] != \"\")][\"Competitor SKU\"]\n", "print(f'Unique Competitor SKUs: {comp_skus.nunique()}')\n", "print(f'Rows with competitor interactions: {len(final_df[final_df[\"Competitor SKU\"].notna() & (final_df[\"Competitor SKU\"] != \"\")])}')\n", "print(f'Rows without competitor interactions: {len(final_df[final_df[\"Competitor SKU\"].isna() | (final_df[\"Competitor SKU\"] == \"\")])}')\n", "\n", "# Show sample of competitor SKU values (should remain individual SKUs in both modes)\n", "comp_sku_sample = final_df[final_df['Competitor SKU'].notna() & (final_df['Competitor SKU'] != '')]['Competitor SKU'].head(5)\n", "print(f'\\nSample Competitor SKU values (should be individual SKUs):')\n", "for i, sku in enumerate(comp_sku_sample, 1):\n", "    print(f'  {i}. {sku}')\n", "    \n", "if not MELT_SKU_FLAG:\n", "    # Check for pipe-joined PROMO IDs (not SKUs)\n", "    if 'Competitor PromoID' in final_df.columns:\n", "        pipe_joined_promos = final_df['Competitor PromoID'].str.contains('\\|', na=False).sum()\n", "        print(f'\\nRows with pipe-joined Competitor PromoIDs (same comp SKU, multiple promos): {pipe_joined_promos}')\n", "        if pipe_joined_promos > 0:\n", "            sample_merged = final_df[final_df['Competitor PromoID'].str.contains('\\|', na=False)][['ABI PromoID', 'Competitor SKU', 'Competitor PromoID']].head(3)\n", "            print(f'Sample merged rows:')\n", "            for _, row in sample_merged.iterrows():\n", "                print(f'  ABI: {row[\"ABI PromoID\"]}')\n", "                print(f'  Comp SKU: {row[\"Competitor SKU\"]}')\n", "                print(f'  Comp PromoIDs: {row[\"Competitor PromoID\"]}')\n", "                print()\n", "    else:\n", "        print('\\nNote: Competitor PromoID column not found in final data')\n", "\n", "print('=' * 50)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3ca1ad52-6707-48e7-9202-0d39fbeb9669", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Retailer</th>\n", "      <th>ABI PromoID</th>\n", "      <th>ABI SKU</th>\n", "      <th>Competitor SKU</th>\n", "      <th>ABI Start</th>\n", "      <th>ABI End</th>\n", "      <th>ABI Coverage</th>\n", "      <th>ABI Mechanic</th>\n", "      <th>ABI Depth</th>\n", "      <th>ABI Rounded</th>\n", "      <th>...</th>\n", "      <th>Competitor End</th>\n", "      <th>Competitor Coverage</th>\n", "      <th>Competitor De<PERSON>h</th>\n", "      <th>Timing</th>\n", "      <th>Weeks since last comp promo</th>\n", "      <th>Weeks until next comp promo</th>\n", "      <th>Overlap Promos</th>\n", "      <th>Actual Overlap Days</th>\n", "      <th>Category</th>\n", "      <th>Competitor Mechanic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>1664 BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>2022-10-19</td>\n", "      <td>2022-10-25</td>\n", "      <td>0.992754</td>\n", "      <td>FID</td>\n", "      <td>26%-30%</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>2022-11-01</td>\n", "      <td>0.956504</td>\n", "      <td>26%-30%</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>-999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>After</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-10-19</td>\n", "      <td>2022-10-25</td>\n", "      <td>0.992754</td>\n", "      <td>FID</td>\n", "      <td>26%-30%</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>2022-11-08</td>\n", "      <td>0.992754</td>\n", "      <td>26%-30%</td>\n", "      <td>-2.0</td>\n", "      <td>999</td>\n", "      <td>-2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Before</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022-04-18</td>\n", "      <td>0.978300</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>2022-05-03</td>\n", "      <td>0.978300</td>\n", "      <td>26%-30%</td>\n", "      <td>-2.0</td>\n", "      <td>999</td>\n", "      <td>-2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Before</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>HEINEKEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022-04-18</td>\n", "      <td>0.978300</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>2022-05-03</td>\n", "      <td>0.978300</td>\n", "      <td>26%-30%</td>\n", "      <td>-2.0</td>\n", "      <td>999</td>\n", "      <td>-2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Before</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-05-11</td>\n", "      <td>2022-05-17</td>\n", "      <td>1.000000</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>2022-06-07</td>\n", "      <td>0.981900</td>\n", "      <td>26%-30%</td>\n", "      <td>2.0</td>\n", "      <td>2</td>\n", "      <td>-999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>After</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 22 columns</p>\n", "</div>"], "text/plain": ["  Retailer                                        ABI PromoID  \\\n", "0   AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "1   AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "2   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "3   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "4   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "\n", "                    ABI SKU                          Competitor SKU  \\\n", "0  BUD BOTTLE (12-15)X250ML        1664 BLONDE BOTTLE (12-15)X250ML   \n", "1  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "2  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "3  BUD BOTTLE (12-15)X250ML    HEINEKEN BLONDE BOTTLE (20-24)X250ML   \n", "4  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "\n", "   ABI Start    ABI End  ABI Coverage ABI Mechanic ABI Depth  ABI Rounded  \\\n", "0 2022-10-19 2022-10-25      0.992754          FID   26%-30%          0.3   \n", "1 2022-10-19 2022-10-25      0.992754          FID   26%-30%          0.3   \n", "2 2022-04-06 2022-04-18      0.978300    Immediate   26%-30%          0.3   \n", "3 2022-04-06 2022-04-18      0.978300    Immediate   26%-30%          0.3   \n", "4 2022-05-11 2022-05-17      1.000000    Immediate   26%-30%          0.3   \n", "\n", "   ... Competitor End Competitor Coverage Competitor Depth  Timing  \\\n", "0  ...     2022-11-01            0.956504          26%-30%     2.0   \n", "1  ...     2022-11-08            0.992754          26%-30%    -2.0   \n", "2  ...     2022-05-03            0.978300          26%-30%    -2.0   \n", "3  ...     2022-05-03            0.978300          26%-30%    -2.0   \n", "4  ...     2022-06-07            0.981900          26%-30%     2.0   \n", "\n", "  Weeks since last comp promo  Weeks until next comp promo  Overlap Promos  \\\n", "0                           2                         -999               0   \n", "1                         999                           -2               0   \n", "2                         999                           -2               0   \n", "3                         999                           -2               0   \n", "4                           2                         -999               0   \n", "\n", "   Actual Overlap Days  Category  Competitor Mechanic  \n", "0                    0     After                  NaN  \n", "1                    0    Before                  NaN  \n", "2                    0    Before                  NaN  \n", "3                    0    Before                  NaN  \n", "4                    0     After                  NaN  \n", "\n", "[5 rows x 22 columns]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6ac9426e-6abd-4821-ae5f-ae9cd2247092", "showTitle": true, "tableResultSettingsMap": {}, "title": "Encode Timing features"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Dummy variables created matching new logic\n", "Available timing columns:\n", "  Overlapping: 343 records\n", "  Same Week: 265 records\n", "  1 wk before: 79 records\n", "  2 wk before: 21 records\n", "  Before: 100 records\n", "  1 wk after: 117 records\n", "  2 wk after: 39 records\n", "  After: 156 records\n"]}], "source": ["# Encode 'Weeks since last comp promo' and 'Weeks until next comp promo' into dummy variables\n", "weeks_since_dummies = pd.get_dummies(final_df['Weeks since last comp promo'], prefix='Wks after')\n", "weeks_until_dummies = pd.get_dummies(final_df['Weeks until next comp promo'], prefix='Wks before')\n", "\n", "# Concatenate the dummy variables with the original dataframe\n", "final_df = pd.concat([final_df, weeks_since_dummies, weeks_until_dummies], axis=1)\n", "\n", "# CORRECTED: Use integer-based column names\n", "final_df['Overlapping'] = final_df[['Wks before_0', 'Wks after_0', 'Overlap Promos']].max(axis=1).astype(int) # Any overlap\n", "final_df['Same Week'] = final_df[['Wks after_1','Wks before_-1']].max(axis=1).astype(int) # Within 7 days but no overlap\n", "final_df['Before'] = final_df[['Wks before_-2', 'Wks before_-3']].max(axis=1) # Group 2 & 3 weeks before as just BEFORE flag\n", "final_df['After'] = final_df[['Wks after_2','Wks after_3']].max(axis=1) # Group 2 & 3 weeks after as just AFTER flag\n", "\n", "# CORRECTED: Rename using new integer-based names\n", "final_df = final_df.rename(columns={\n", "    'Wks after_2': '1 wk after',\n", "    'Wks after_3': '2 wk after',       \n", "    'Wks before_-2': '1 wk before',\n", "    'Wks before_-3': '2 wk before'   \n", "})\n", "\n", "# CORRECTED: Drop columns using new integer-based names\n", "final_df = final_df.drop(columns=['Wks after_999', 'Wks before_-999', 'Wks after_1','Wks before_-1', 'Wks before_0', 'Wks after_0'])\n", "\n", "print(\"✅ Dummy variables created matching new logic\")\n", "print(\"Available timing columns:\")\n", "timing_cols = ['Overlapping', 'Same Week', '1 wk before', '2 wk before', 'Before', '1 wk after', '2 wk after', 'After']\n", "\n", "for col in timing_cols:\n", "    if col in final_df.columns:\n", "        count = final_df[col].sum()\n", "        print(f\"  {col}: {count} records\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- DataFrame Info ---\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 885 entries, 0 to 884\n", "Data columns (total 30 columns):\n", " #   Column                       Non-Null Count  Dtype         \n", "---  ------                       --------------  -----         \n", " 0   Retailer                     885 non-null    object        \n", " 1   ABI PromoID                  885 non-null    object        \n", " 2   ABI SKU                      885 non-null    object        \n", " 3   Competitor SKU               885 non-null    object        \n", " 4   ABI Start                    885 non-null    datetime64[ns]\n", " 5   ABI End                      885 non-null    datetime64[ns]\n", " 6   ABI Coverage                 885 non-null    float64       \n", " 7   ABI Mechanic                 885 non-null    object        \n", " 8   ABI Depth                    885 non-null    object        \n", " 9   ABI Rounded                  885 non-null    float64       \n", " 10  Competitor PromoID           885 non-null    object        \n", " 11  Competitor Start             819 non-null    datetime64[ns]\n", " 12  Competitor End               819 non-null    datetime64[ns]\n", " 13  Competitor Coverage          819 non-null    float64       \n", " 14  Competitor De<PERSON>h             819 non-null    object        \n", " 15  Timing                       819 non-null    float64       \n", " 16  Weeks since last comp promo  885 non-null    int64         \n", " 17  Weeks until next comp promo  885 non-null    int64         \n", " 18  Overlap Promos               885 non-null    int64         \n", " 19  Actual Overlap Days          885 non-null    int64         \n", " 20  Category                     885 non-null    object        \n", " 21  Competitor Mechanic          0 non-null      object        \n", " 22  1 wk after                   885 non-null    bool          \n", " 23  2 wk after                   885 non-null    bool          \n", " 24  2 wk before                  885 non-null    bool          \n", " 25  1 wk before                  885 non-null    bool          \n", " 26  Overlapping                  885 non-null    int32         \n", " 27  Same Week                    885 non-null    int32         \n", " 28  Before                       885 non-null    bool          \n", " 29  After                        885 non-null    bool          \n", "dtypes: bool(6), datetime64[ns](4), float64(4), int32(2), int64(4), object(10)\n", "memory usage: 164.3+ KB\n", "\n", "\n", "--- Category Distribution ---\n", "Value counts for the 'Category' column:\n", "Category\n", "Overlap           298\n", "After             294\n", "Before            227\n", "No interaction     66\n", "Name: count, dtype: int64\n", "\n", "\n", "--- Timing Column Value Distribution ---\n", "Value counts for 'Weeks since last comp promo':\n", "Weeks since last comp promo\n", "0      298\n", "1      138\n", "2      117\n", "3       39\n", "999    293\n", "Name: count, dtype: int64\n", "\n", "Value counts for 'Weeks until next comp promo':\n", "Weeks until next comp promo\n", "-999    360\n", "-3       21\n", "-2       79\n", "-1      127\n", " 0      298\n", "Name: count, dtype: int64\n", "\n", "\n", "--- Overlap Analysis ---\n", "Number of rows with 'Actual Overlap Days' > 0: 343\n", "\n", "--- Sample of overlapping rows (if any) ---\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Retailer</th>\n", "      <th>ABI PromoID</th>\n", "      <th>ABI SKU</th>\n", "      <th>Competitor SKU</th>\n", "      <th>ABI Start</th>\n", "      <th>ABI End</th>\n", "      <th>ABI Coverage</th>\n", "      <th>ABI Mechanic</th>\n", "      <th>ABI Depth</th>\n", "      <th>ABI Rounded</th>\n", "      <th>...</th>\n", "      <th>Category</th>\n", "      <th>Competitor Mechanic</th>\n", "      <th>1 wk after</th>\n", "      <th>2 wk after</th>\n", "      <th>2 wk before</th>\n", "      <th>1 wk before</th>\n", "      <th>Overlapping</th>\n", "      <th>Same Week</th>\n", "      <th>Before</th>\n", "      <th>After</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-12-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-12-27</td>\n", "      <td>2022-12-31</td>\n", "      <td>0.942029</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>Overlap</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-03-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2024-03-26</td>\n", "      <td>2024-04-01</td>\n", "      <td>0.992647</td>\n", "      <td>LV</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>Overlap</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2024-04-23</td>\n", "      <td>2024-05-06</td>\n", "      <td>0.985294</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>Overlap</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>HEINEKEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2024-04-23</td>\n", "      <td>2024-05-06</td>\n", "      <td>0.985294</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>After</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-09-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2024-09-17</td>\n", "      <td>2024-09-23</td>\n", "      <td>0.987654</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>Overlap</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   Retailer                                        ABI PromoID  \\\n", "9    AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-12-...   \n", "24   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-03-...   \n", "28   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-04-...   \n", "29   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-04-...   \n", "33   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-09-...   \n", "\n", "                     ABI SKU                          Competitor SKU  \\\n", "9   BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "24  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "28  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "29  BUD BOTTLE (12-15)X250ML    HEINEKEN BLONDE BOTTLE (20-24)X250ML   \n", "33  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "\n", "    ABI Start    ABI End  ABI Coverage ABI Mechanic ABI Depth  ABI Rounded  \\\n", "9  2022-12-27 2022-12-31      0.942029           LV   21%-25%         0.25   \n", "24 2024-03-26 2024-04-01      0.992647           LV   26%-30%         0.30   \n", "28 2024-04-23 2024-05-06      0.985294    Immediate   26%-30%         0.30   \n", "29 2024-04-23 2024-05-06      0.985294    Immediate   26%-30%         0.30   \n", "33 2024-09-17 2024-09-23      0.987654    Immediate   26%-30%         0.30   \n", "\n", "    ... Category Competitor Mechanic 1 wk after  2 wk after 2 wk before  \\\n", "9   ...  <PERSON>lap                 NaN      False       False       False   \n", "24  ...  Overlap                 NaN      False       False       False   \n", "28  ...  <PERSON>lap                 NaN      False       False       False   \n", "29  ...    After                 NaN      False        True       False   \n", "33  ...  <PERSON>lap                 NaN      False       False       False   \n", "\n", "    1 wk before  Overlapping  Same Week  Before  After  \n", "9         False            1          0   False  False  \n", "24        False            1          0   False  False  \n", "28        False            1          0   False  False  \n", "29        False            1          0   False   True  \n", "33        False            1          0   False  False  \n", "\n", "[5 rows x 30 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "--- Final DataFrame Columns ---\n", "['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU', 'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic', 'ABI Depth', 'ABI Rounded', 'Competitor PromoID', 'Competitor Start', 'Competitor End', 'Competitor Coverage', 'Competitor Depth', 'Timing', 'Weeks since last comp promo', 'Weeks until next comp promo', 'Overlap Promos', 'Actual Overlap Days', 'Category', 'Competitor Mechanic', '1 wk after', '2 wk after', '2 wk before', '1 wk before', 'Overlapping', 'Same Week', 'Before', 'After']\n"]}], "source": ["\n", "#DEBUG\n", "\n", "# --- DEBUG CELL ---\n", "# This cell will help us inspect the dataframe created by the new timing logic\n", "# to understand why the downstream cells are failing.\n", "\n", "print(\"--- DataFrame Info ---\")\n", "final_df.info()\n", "\n", "print(\"\\n\\n--- Category Distribution ---\")\n", "print(\"Value counts for the 'Category' column:\")\n", "print(final_df['Category'].value_counts(dropna=False))\n", "\n", "print(\"\\n\\n--- Timing Column Value Distribution ---\")\n", "print(\"Value counts for 'Weeks since last comp promo':\")\n", "print(final_df['Weeks since last comp promo'].value_counts(dropna=False).sort_index())\n", "\n", "print(\"\\nValue counts for 'Weeks until next comp promo':\")\n", "print(final_df['Weeks until next comp promo'].value_counts(dropna=False).sort_index())\n", "\n", "\n", "print(\"\\n\\n--- Overlap Analysis ---\")\n", "print(f\"Number of rows with 'Actual Overlap Days' > 0: {len(final_df[final_df['Actual Overlap Days'] > 0])}\")\n", "\n", "print(\"\\n--- Sample of overlapping rows (if any) ---\")\n", "display(final_df[final_df['Actual Overlap Days'] > 0].head())\n", "\n", "\n", "print(\"\\n\\n--- Final DataFrame Columns ---\")\n", "print(list(final_df.columns))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9eb766c2-ce2d-4781-90e1-3244a630d14c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Removed redundant Legacy Code\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "23b70cfb-49f8-4922-a8b5-ec517e5069f4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All timing features already created with clean logic in Previous Cell\n", "Skipping legacy dummy variable encoding\n", "\n", "Timing features summary:\n", "Overlapping: 343 interactions\n", "Same Week: 265 interactions\n", "1 wk before: 79 interactions\n", "2 wk before: 21 interactions\n", "Before: 100 interactions\n", "1 wk after: 117 interactions\n", "2 wk after: 39 interactions\n", "After: 156 interactions\n"]}], "source": ["print(\"All timing features already created with clean logic in Previous Cell\")\n", "print(\"Skipping legacy dummy variable encoding\")\n", "\n", "print(f\"\\nTiming features summary:\")\n", "timing_features = ['Overlapping', 'Same Week', '1 wk before', '2 wk before', 'Before', '1 wk after', '2 wk after', 'After']\n", "# timing_features = ['Overlapping', 'Same Week', '1 wk before', '2 wk before',\"3 wk before\", 'Before', '1 wk after', '2 wk after',\"3 wk after\", 'After']\n", "for feature in timing_features:\n", "    count = final_df[feature].sum()\n", "    print(f\"{feature}: {count} interactions\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Flag value           : False\n", "Rows in final_df     : 885\n", "Distinct ABI SKU     : 5\n", "Rows / ABI SKU ratio : 177.0\n", "Sample competitor list for one ABI promo:\n", "                 ABI SKU                         Competitor SKU\n", "BUD BOTTLE (12-15)X250ML       1664 BLONDE BOTTLE (12-15)X250ML\n", "BUD BOTTLE (12-15)X250ML GRIMBERGEN BLONDE BOTTLE (20-24)X250ML\n", "--------------------------------------------------\n"]}], "source": ["# DEBUG – row counters ───────────────────────────────────\n", "print(\"Flag value           :\", MELT_SKU_FLAG)\n", "print(\"Rows in final_df     :\", len(final_df))\n", "print(\"Distinct ABI SKU     :\", final_df['ABI SKU'].nunique())\n", "print(\"Rows / ABI SKU ratio :\", len(final_df) / final_df['ABI SKU'].nunique())\n", "print(\"Sample competitor list for one ABI promo:\")\n", "sample = (final_df\n", "          .loc[final_df['ABI PromoID'] == final_df['ABI PromoID'].iloc[0],\n", "               ['ABI SKU', 'Competitor SKU']]\n", "          .head())\n", "print(sample.to_string(index=False))\n", "print(\"--------------------------------------------------\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "16daceda-85b8-42f4-a42b-22ccd07b84f0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating final model features from the new 'Category' and 'Timing' columns...\n", "\n", "Successfully created new feature columns.\n", "Displaying the results:\n", "                                         ABI PromoID  \\\n", "0  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "1  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "2  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "3  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "4  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "5  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "6  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "7  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "8  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "9  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-12-...   \n", "\n", "                           Competitor SKU Category  Timing  Same week  \\\n", "0        1664 BLONDE BOTTLE (12-15)X250ML    After     2.0          0   \n", "1  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   Before    -2.0          0   \n", "2  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   Before    -2.0          0   \n", "3    HEINEKEN BLONDE BOTTLE (20-24)X250ML   Before    -2.0          0   \n", "4  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML    After     2.0          0   \n", "5    HEINEKEN BLONDE BOTTLE (20-24)X250ML    After     2.0          0   \n", "6        1664 BLONDE BOTTLE (12-15)X250ML   Before    -2.0          0   \n", "7    HEINEKEN BLONDE BOTTLE (20-24)X250ML    After     3.0          0   \n", "8  LA GOUDALE BLONDE BOTTLE (12-15)X250ML    After     2.0          0   \n", "9  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML  Overlap     0.0          1   \n", "\n", "   Overlapping  Before  After  1 wk before  2 wk before  1 wk after  \\\n", "0            0       1      0            0            1           0   \n", "1            0       0      1            0            0           0   \n", "2            0       0      1            0            0           0   \n", "3            0       0      1            0            0           0   \n", "4            0       1      0            0            1           0   \n", "5            0       1      0            0            1           0   \n", "6            0       0      1            0            0           0   \n", "7            0       1      0            0            0           0   \n", "8            0       1      0            0            1           0   \n", "9            1       0      0            0            0           0   \n", "\n", "   2 wk after  \n", "0           0  \n", "1           1  \n", "2           1  \n", "3           1  \n", "4           0  \n", "5           0  \n", "6           1  \n", "7           0  \n", "8           0  \n", "9           0  \n"]}, {"data": {"text/plain": ["(885, 31)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Creating final model features from the new 'Category' and 'Timing' columns...\")\n", "\n", "\n", "# It's 1 if the promotion was an 'Overlap' or started in the 'Same Week Start'.\n", "final_df['Same week'] = ((final_df['Category'] == 'Overlap') | (final_df['Category'] == 'Same Week Start')).astype(int)\n", "\n", "# 2. Create the other feature columns directly from the clean 'Timing' data.\n", "final_df['1 wk before'] = (final_df['Timing'] == 1).astype(int)\n", "final_df['2 wk before'] = (final_df['Timing'] == 2).astype(int)\n", "# final_df['3 wk before'] = (final_df['Timing'] == 3).astype(int) \n", "final_df['Before'] = ((final_df['Timing'] == 1) | (final_df['Timing'] == 2) | (final_df['Timing'] == 3)).astype(int)  \n", "\n", "final_df['1 wk after'] = (final_df['Timing'] == -1).astype(int)\n", "final_df['2 wk after'] = (final_df['Timing'] == -2).astype(int)\n", "# final_df['3 wk after'] = (final_df['Timing'] == -3).astype(int) \n", "final_df['After'] = ((final_df['Timing'] == -1) | (final_df['Timing'] == -2) | (final_df['Timing'] == -3)).astype(int)  \n", "\n", "# For any downstream code that might still look for a column named 'Overlapping',\n", "# we can create it as a copy of our new 'Same week' column.\n", "final_df['Overlapping'] = final_df['Same week']\n", "\n", "\n", "# 3. Display the results to confirm.\n", "# We select the most relevant columns to show the successful creation of the new features.\n", "print(\"\\nSuccessfully created new feature columns.\")\n", "print(\"Displaying the results:\")\n", "\n", "display_cols = [\n", "    'ABI PromoID', 'Competitor SKU', 'Category', 'Timing', 'Same week',\n", "    'Overlapping', 'Before', 'After', '1 wk before', '2 wk before',\n", "    '1 wk after', '2 wk after'\n", "]\n", "\n", "# display_cols = [\n", "#     'ABI PromoID', 'Competitor SKU', 'Category', 'Timing', 'Same week',\n", "#     'Overlapping', 'Before', 'After', '1 wk before', '2 wk before', '3 wk before',  # ADD 3 wk before\n", "#     '1 wk after', '2 wk after', '3 wk after'\n", "# ]\n", "print(final_df[display_cols].head(10))\n", "final_df.shape\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3b86ed37-a830-4927-9a9d-1c5e911b386f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Retailer</th>\n", "      <th>ABI PromoID</th>\n", "      <th>ABI SKU</th>\n", "      <th>Competitor SKU</th>\n", "      <th>ABI Start</th>\n", "      <th>ABI End</th>\n", "      <th>ABI Coverage</th>\n", "      <th>ABI Mechanic</th>\n", "      <th>ABI Depth</th>\n", "      <th>ABI Rounded</th>\n", "      <th>...</th>\n", "      <th>Competitor Mechanic</th>\n", "      <th>1 wk after</th>\n", "      <th>2 wk after</th>\n", "      <th>2 wk before</th>\n", "      <th>1 wk before</th>\n", "      <th>Overlapping</th>\n", "      <th>Same Week</th>\n", "      <th>Before</th>\n", "      <th>After</th>\n", "      <th>Same week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>1664 BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>2022-10-19</td>\n", "      <td>2022-10-25</td>\n", "      <td>0.992754</td>\n", "      <td>FID</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-10-19</td>\n", "      <td>2022-10-25</td>\n", "      <td>0.992754</td>\n", "      <td>FID</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022-04-18</td>\n", "      <td>0.978300</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>HEINEKEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022-04-18</td>\n", "      <td>0.978300</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN</td>\n", "      <td>AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...</td>\n", "      <td>BUD BOTTLE (12-15)X250ML</td>\n", "      <td>GRIMBERGEN BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>2022-05-11</td>\n", "      <td>2022-05-17</td>\n", "      <td>1.000000</td>\n", "      <td>Immediate</td>\n", "      <td>26%-30%</td>\n", "      <td>0.30</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>880</th>\n", "      <td>SUPER U</td>\n", "      <td>SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...</td>\n", "      <td>LEFFE BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td></td>\n", "      <td>2024-03-05</td>\n", "      <td>2024-03-17</td>\n", "      <td>0.969402</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>881</th>\n", "      <td>SUPER U</td>\n", "      <td>SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...</td>\n", "      <td>LEFFE BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td></td>\n", "      <td>2024-11-19</td>\n", "      <td>2024-12-01</td>\n", "      <td>0.975207</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>882</th>\n", "      <td>SUPER U</td>\n", "      <td>SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...</td>\n", "      <td>LEFFE BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td></td>\n", "      <td>2025-03-11</td>\n", "      <td>2025-03-23</td>\n", "      <td>0.972973</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>883</th>\n", "      <td>SUPERMARCHES MATCH</td>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>LEFFE BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td></td>\n", "      <td>2024-10-08</td>\n", "      <td>2024-10-20</td>\n", "      <td>1.000000</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>SUPERMARCHES MATCH</td>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>LEFFE BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td></td>\n", "      <td>2022-10-11</td>\n", "      <td>2022-10-23</td>\n", "      <td>0.956900</td>\n", "      <td>LV</td>\n", "      <td>21%-25%</td>\n", "      <td>0.25</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>885 rows × 31 columns</p>\n", "</div>"], "text/plain": ["               Retailer                                        ABI PromoID  \\\n", "0                AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "1                AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "2                AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "3                AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "4                AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "..                  ...                                                ...   \n", "880             SUPER U  SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...   \n", "881             SUPER U  SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...   \n", "882             SUPER U  SUPER U_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE...   \n", "883  SUPERMARCHES MATCH  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...   \n", "884  SUPERMARCHES MATCH  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...   \n", "\n", "                               ABI SKU  \\\n", "0             BUD BOTTLE (12-15)X250ML   \n", "1             BUD BOTTLE (12-15)X250ML   \n", "2             BUD BOTTLE (12-15)X250ML   \n", "3             BUD BOTTLE (12-15)X250ML   \n", "4             BUD BOTTLE (12-15)X250ML   \n", "..                                 ...   \n", "880  LEFFE BLONDE BOTTLE (20-24)X250ML   \n", "881  LEFFE BLONDE BOTTLE (20-24)X250ML   \n", "882  LEFFE BLONDE BOTTLE (20-24)X250ML   \n", "883  LEFFE BLONDE BOTTLE (12-15)X250ML   \n", "884  LEFFE BLONDE BOTTLE (20-24)X250ML   \n", "\n", "                             Competitor SKU  ABI Start    ABI End  \\\n", "0          1664 BLONDE BOTTLE (12-15)X250ML 2022-10-19 2022-10-25   \n", "1    GRIMBERGEN BLONDE BOTTLE (20-24)X250ML 2022-10-19 2022-10-25   \n", "2    GRIMBERGEN BLONDE BOTTLE (20-24)X250ML 2022-04-06 2022-04-18   \n", "3      HEINEKEN BLONDE BOTTLE (20-24)X250ML 2022-04-06 2022-04-18   \n", "4    GRIMBERGEN BLONDE BOTTLE (20-24)X250ML 2022-05-11 2022-05-17   \n", "..                                      ...        ...        ...   \n", "880                                         2024-03-05 2024-03-17   \n", "881                                         2024-11-19 2024-12-01   \n", "882                                         2025-03-11 2025-03-23   \n", "883                                         2024-10-08 2024-10-20   \n", "884                                         2022-10-11 2022-10-23   \n", "\n", "     ABI Coverage ABI Mechanic ABI Depth  ABI Rounded  ...  \\\n", "0        0.992754          FID   26%-30%         0.30  ...   \n", "1        0.992754          FID   26%-30%         0.30  ...   \n", "2        0.978300    Immediate   26%-30%         0.30  ...   \n", "3        0.978300    Immediate   26%-30%         0.30  ...   \n", "4        1.000000    Immediate   26%-30%         0.30  ...   \n", "..            ...          ...       ...          ...  ...   \n", "880      0.969402           LV   21%-25%         0.25  ...   \n", "881      0.975207           LV   21%-25%         0.25  ...   \n", "882      0.972973           LV   21%-25%         0.25  ...   \n", "883      1.000000           LV   21%-25%         0.25  ...   \n", "884      0.956900           LV   21%-25%         0.25  ...   \n", "\n", "    Competitor Mechanic 1 wk after 2 wk after  2 wk before 1 wk before  \\\n", "0                   NaN          0          0            1           0   \n", "1                   NaN          0          1            0           0   \n", "2                   NaN          0          1            0           0   \n", "3                   NaN          0          1            0           0   \n", "4                   NaN          0          0            1           0   \n", "..                  ...        ...        ...          ...         ...   \n", "880                 NaN          0          0            0           0   \n", "881                 NaN          0          0            0           0   \n", "882                 NaN          0          0            0           0   \n", "883                 NaN          0          0            0           0   \n", "884                 NaN          0          0            0           0   \n", "\n", "     Overlapping  Same Week  Before  After  Same week  \n", "0              0          0       1      0          0  \n", "1              0          0       0      1          0  \n", "2              0          0       0      1          0  \n", "3              0          0       0      1          0  \n", "4              0          0       1      0          0  \n", "..           ...        ...     ...    ...        ...  \n", "880            0          0       0      0          0  \n", "881            0          0       0      0          0  \n", "882            0          0       0      0          0  \n", "883            0          0       0      0          0  \n", "884            0          0       0      0          0  \n", "\n", "[885 rows x 31 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "749feb7f-ab7d-4ad1-8eba-cd4e3dbf4983", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Ensure all timing flags are present in final_df before aggregation\n", "for col in ['Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']:\n", "    if col not in final_df.columns:\n", "        final_df[col] = 0  # Default to 0 if not present\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Retailer                                        ABI PromoID  \\\n", "0   AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "1   AUCHAN  AUCHAN_BUD_(blank)_BIERE BLONDE EDITION LIMITE...   \n", "2   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "3   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-04-...   \n", "4   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "5   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-05-...   \n", "6   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "7   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "8   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-09-...   \n", "9   AUCHAN  AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2022-12-...   \n", "\n", "                    ABI SKU                          Competitor SKU  \\\n", "0  BUD BOTTLE (12-15)X250ML        1664 BLONDE BOTTLE (12-15)X250ML   \n", "1  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "2  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "3  BUD BOTTLE (12-15)X250ML    HEINEKEN BLONDE BOTTLE (20-24)X250ML   \n", "4  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "5  BUD BOTTLE (12-15)X250ML    HEINEKEN BLONDE BOTTLE (20-24)X250ML   \n", "6  BUD BOTTLE (12-15)X250ML        1664 BLONDE BOTTLE (12-15)X250ML   \n", "7  BUD BOTTLE (12-15)X250ML    HEINEKEN BLONDE BOTTLE (20-24)X250ML   \n", "8  BUD BOTTLE (12-15)X250ML  LA GOUDALE BLONDE BOTTLE (12-15)X250ML   \n", "9  BUD BOTTLE (12-15)X250ML  GRIMBERGEN BLONDE BOTTLE (20-24)X250ML   \n", "\n", "   ABI Start    ABI End  ABI Coverage ABI Mechanic ABI Depth  ABI Rounded  \\\n", "0 2022-10-19 2022-10-25      0.992754          FID   26%-30%         0.30   \n", "1 2022-10-19 2022-10-25      0.992754          FID   26%-30%         0.30   \n", "2 2022-04-06 2022-04-18      0.978300    Immediate   26%-30%         0.30   \n", "3 2022-04-06 2022-04-18      0.978300    Immediate   26%-30%         0.30   \n", "4 2022-05-11 2022-05-17      1.000000    Immediate   26%-30%         0.30   \n", "5 2022-05-11 2022-05-17      1.000000    Immediate   26%-30%         0.30   \n", "6 2022-09-21 2022-09-27      0.942000    Immediate   26%-30%         0.30   \n", "7 2022-09-21 2022-09-27      0.942000    Immediate   26%-30%         0.30   \n", "8 2022-09-21 2022-09-27      0.942000    Immediate   26%-30%         0.30   \n", "9 2022-12-27 2022-12-31      0.942029           LV   21%-25%         0.25   \n", "\n", "   ... Competitor Mechanic 1 wk after 2 wk after  2 wk before 1 wk before  \\\n", "0  ...                 NaN          0          0            1           0   \n", "1  ...                 NaN          0          1            0           0   \n", "2  ...                 NaN          0          1            0           0   \n", "3  ...                 NaN          0          1            0           0   \n", "4  ...                 NaN          0          0            1           0   \n", "5  ...                 NaN          0          0            1           0   \n", "6  ...                 NaN          0          1            0           0   \n", "7  ...                 NaN          0          0            0           0   \n", "8  ...                 NaN          0          0            1           0   \n", "9  ...                 NaN          0          0            0           0   \n", "\n", "   Overlapping  Same Week  Before  After  Same week  \n", "0            0          0       1      0          0  \n", "1            0          0       0      1          0  \n", "2            0          0       0      1          0  \n", "3            0          0       0      1          0  \n", "4            0          0       1      0          0  \n", "5            0          0       1      0          0  \n", "6            0          0       0      1          0  \n", "7            0          0       1      0          0  \n", "8            0          0       1      0          0  \n", "9            1          0       0      0          1  \n", "\n", "[10 rows x 31 columns]\n"]}], "source": ["print(final_df.head(10))"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4cd67a1e-9b5b-4937-9d9a-ac1242381404", "showTitle": true, "tableResultSettingsMap": {}, "title": "Assemble ADS + Add weather & holidays"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating cleaned ADS - removing artificial permutations...\n", "Processing interaction and no-interaction records separately...\n", "Interaction records to process: 819\n", "No-interaction records to preserve: 66\n", "Preserved 66 individual no-interaction records\n", "Final ads_df: 885 records\n", "  - Interaction records: 819\n", "  - No-interaction records: 66\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:60: FutureWarning: The provided callable <function max at 0x000001F2453243A0> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"max\" instead.\n", "  ).agg({\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", " CLEANUP: Nullifying competitor data for zero-interaction records...\n", "Found 60 records with competitor SKU but zero timing interactions\n", "Setting all competitor fields to NULL for these orphaned records...\n", " Cleaned 60 orphaned records - competitor fields set to NULL\n", "\n", " CLEANUP: Removing duplicate no-interaction records...\n", "Found 8 ABI PromoIDs with duplicate no-interaction records\n", "  Removed 3 duplicate no-interaction records for AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12 BTL 25 CL_2024-10-08_2024-10-14\n", "  Removed 1 duplicate no-interaction records for AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12 BTL 25 CL_2024-11-19_2024-11-25\n", "  Removed 1 duplicate no-interaction records for AUCHAN SM + SIMPLY MARKET_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE_20 BTL 25 CL_2022-09-21_2022-09-27\n", "  Removed 2 duplicate no-interaction records for AUCHAN_BUD_(blank)_BIERE_12 BTL 25 CL_2024-10-08_2024-10-14\n", "  Removed 2 duplicate no-interaction records for AUCHAN_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE_12 BTL 25 CL_2023-06-20_2023-06-26\n", "  Removed 1 duplicate no-interaction records for AUCHAN_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE_12 BTL 25 CL_2023-08-15_2023-08-21\n", "  Removed 1 duplicate no-interaction records for AUCHAN_LEFFE_LEFFE BLONDE_BIERE BLONDE ABBAYE_20 BTL 25 CL_2022-09-21_2022-09-27\n", "  Removed 1 duplicate no-interaction records for CORA + RECORD_BUD_(blank)_BIERE_12 BTL 25 CL_2022-06-21_2022-06-27\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\3063696254.py:164: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n"]}], "source": ["print(\"Creating cleaned ADS - removing artificial permutations...\")\n", "\n", "# FIX: Use np.max instead of the string 'max' to avoid internal pandas error\n", "import numpy as np\n", "\n", "# ads_df = final_df.groupby(\n", "#     ['Retailer',\n", "#      'ABI PromoID',\n", "#      'ABI SKU',\n", "#      'ABI Start',\n", "#      'ABI End',\n", "#      'ABI Coverage',\n", "#      'ABI Mechanic',\n", "#      'ABI Depth',\n", "#      'ABI Rounded',\n", "#      'Competitor SKU'\n", "#     ],\n", "#     as_index=False\n", "# ).agg({\n", "#     'Competitor Coverage': np.max,\n", "#     'Competitor Mechanic': 'first',\n", "#     'Competitor Depth': 'first',\n", "#     'Overlapping':     np.max,\n", "#     'Same Week':       np.max,\n", "#     '1 wk after':      np.max,\n", "#     '2 wk after':      np.max,\n", "#     '1 wk before':     np.max,\n", "#     '2 wk before':     np.max,\n", "#     'Actual Overlap Days': np.max\n", "# })\n", "\n", "\n", "# FIXED: Handle interaction and no-interaction records separately to preserve all records\n", "print(\"Processing interaction and no-interaction records separately...\")\n", "\n", "# Separate interaction and no-interaction records\n", "interaction_records = final_df[final_df['Competitor SKU'].notna() & (final_df['Competitor SKU'] != '')].copy()\n", "no_interaction_records = final_df[(final_df['Competitor SKU'].isnull()) | (final_df['Competitor SKU'] == '')].copy()\n", "\n", "print(f\"Interaction records to process: {len(interaction_records)}\")\n", "print(f\"No-interaction records to preserve: {len(no_interaction_records)}\")\n", "\n", "# Process interaction records with groupby (these can have multiple competitor interactions per promo)\n", "if len(interaction_records) > 0:\n", "    ads_df_interactions = interaction_records.groupby(\n", "        ['Retailer',\n", "         'ABI PromoID',\n", "         'ABI SKU',\n", "         'ABI Start',\n", "         'ABI End',\n", "         'ABI Coverage',\n", "         'ABI Mechanic',\n", "         'ABI Depth',\n", "         'ABI Rounded',\n", "         'Competitor SKU',\n", "         'Competitor Start',\n", "         'Competitor End'\n", "        ],\n", "        as_index=False\n", "    ).agg({\n", "        'Competitor Start':      'first',\n", "        'Competitor End':        'first',\n", "        'Competitor Coverage':   np.max,\n", "        'Competitor Mechanic':   'first',\n", "        'Competitor De<PERSON>h':      'first',\n", "        'Overlapping':           np.max,\n", "        'Same Week':             np.max,\n", "        '1 wk after':            np.max,\n", "        '2 wk after':            np.max,\n", "        '1 wk before':           np.max,\n", "        '2 wk before':           np.max,\n", "        'Actual Overlap Days':   np.max\n", "    })\n", "else:\n", "    ads_df_interactions = pd.DataFrame()\n", "\n", "# Process no-interaction records (keep each record individually - NO GROUPING)\n", "if len(no_interaction_records) > 0:\n", "    # Don't group no-interaction records - keep them as individual records\n", "    ads_df_no_interactions = no_interaction_records.copy()\n", "    print(f\"Preserved {len(ads_df_no_interactions)} individual no-interaction records\")\n", "else:\n", "    ads_df_no_interactions = pd.DataFrame()\n", "\n", "# Combine both datasets\n", "ads_df = pd.concat([ads_df_interactions, ads_df_no_interactions], ignore_index=True)\n", "\n", "print(f\"Final ads_df: {len(ads_df)} records\")\n", "print(f\"  - Interaction records: {len(ads_df_interactions)}\")\n", "print(f\"  - No-interaction records: {len(ads_df_no_interactions)}\")\n", "\n", "# Add weather and holiday data\n", "weather['date'] = pd.to_datetime(weather['date'], format='ISO8601')\n", "ads_df['Avg Temp'] = ads_df.apply(\n", "    lambda row: weather[\n", "        (weather['date'] >= row['ABI Start']) & \n", "        (weather['date'] <= row['ABI End'])\n", "    ]['temp_c'].mean(), axis=1\n", ")\n", "\n", "ads_df['KSM'] = ads_df.apply(\n", "    lambda row: 1 if not holidays[\n", "    (holidays['date'] >= row['ABI Start'] - pd.<PERSON><PERSON><PERSON>(days=7)) &\n", "    (holidays['date'] <= row['ABI End'])\n", "    ].empty else 0, axis=1\n", ")\n", "\n", "\n", "print(\"\\n CLEANUP: Nullifying competitor data for zero-interaction records...\")\n", "\n", "# Define timing columns to check\n", "timing_check_cols = ['1 wk before', '2 wk before', '1 wk after', '2 wk after', 'Overlapping']\n", "\n", "# Find records with competitor SKU but all timing flags = 0\n", "# Also check for empty string in Competitor SKU, which denotes 'No Interaction' grouped records\n", "orphaned_mask = (\n", "    (ads_df['Competitor SKU'].notna()) & \n", "    (ads_df['Competitor SKU'] != '') &\n", "    (ads_df[timing_check_cols].sum(axis=1) == 0)\n", ")\n", "\n", "orphaned_count = orphaned_mask.sum()\n", "print(f\"Found {orphaned_count} records with competitor SKU but zero timing interactions\")\n", "\n", "if orphaned_count > 0:\n", "    print(\"Setting all competitor fields to NULL for these orphaned records...\")\n", "    \n", "    # Nuke all competitor fields for orphaned records\n", "    competitor_cols = ['Competitor SKU', 'Competitor Coverage', 'Competitor Mechanic', 'Competitor Depth']\n", "    for col in competitor_cols:\n", "        if col in ads_df.columns:\n", "            if pd.api.types.is_numeric_dtype(ads_df[col]):\n", "                ads_df.loc[orphaned_mask, col] = np.nan\n", "            else:\n", "                ads_df.loc[orphaned_mask, col] = None # Use None for object columns\n", "    \n", "    print(f\" Cleaned {orphaned_count} orphaned records - competitor fields set to NULL\")\n", "else:\n", "    print(\"No orphaned records found - data is already clean\")\n", "\n", "# FIXED: Remove duplicate no-interaction records properly\n", "print(\"\\n CLEANUP: Removing duplicate no-interaction records...\")\n", "\n", "def remove_duplicate_no_interactions(df):\n", "    \"\"\"\n", "    Remove duplicate no-interaction records while preserving legitimate ones.\n", "    Only removes records that are actually duplicates (multiple no-interaction records for same promo),\n", "    not legitimate no-interaction records when the same promo has both interaction and no-interaction records.\n", "    \"\"\"\n", "    # Identify no-interaction records (where Competitor SKU is null/empty)\n", "    no_interaction_mask = df['Competitor SKU'].isnull() | (df['Competitor SKU'] == '')\n", "    \n", "    # Count no-interaction records per ABI PromoID\n", "    no_interaction_counts = df[no_interaction_mask].groupby('ABI PromoID').size()\n", "    \n", "    # Find ABI PromoIDs that have multiple no-interaction records (actual duplicates)\n", "    duplicate_no_interactions = no_interaction_counts[no_interaction_counts > 1].index.tolist()\n", "    \n", "    if len(duplicate_no_interactions) > 0:\n", "        print(f\"Found {len(duplicate_no_interactions)} ABI PromoIDs with duplicate no-interaction records\")\n", "        \n", "        # For each promo with duplicates, keep only the first no-interaction record\n", "        for promo_id in duplicate_no_interactions:\n", "            duplicate_records = df[(df['ABI PromoID'] == promo_id) & no_interaction_mask]\n", "            if len(duplicate_records) > 1:\n", "                # Keep the first record, remove the rest\n", "                indices_to_remove = duplicate_records.index[1:]  # Skip the first one\n", "                df = df.drop(indices_to_remove)\n", "                print(f\"  Removed {len(indices_to_remove)} duplicate no-interaction records for {promo_id}\")\n", "    else:\n", "        print(\"No duplicate no-interaction records found\")\n", "    \n", "    return df\n", "\n", "# Apply the duplicate removal\n", "ads_df = remove_duplicate_no_interactions(ads_df)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2c7d9f06-cb0f-437c-afcc-33a55513fb26", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Old, missing some renames\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Market Description</th>\n", "      <th>date_week_start</th>\n", "      <th>date_week_end</th>\n", "      <th>sku</th>\n", "      <th>Promo_Value</th>\n", "      <th>Non_Promo_Price</th>\n", "      <th>Promo_Price</th>\n", "      <th>Non_Promo_Value</th>\n", "      <th>Promo_Volume</th>\n", "      <th>Non_Promo_Volume</th>\n", "      <th>...</th>\n", "      <th>Non_Promo_Price_PPC</th>\n", "      <th>Promo_Price_PPC</th>\n", "      <th>volume_hl</th>\n", "      <th>promo_volume_hl</th>\n", "      <th>non_promo_volume_hl</th>\n", "      <th>base_volume_hl</th>\n", "      <th>value_eur</th>\n", "      <th>w_dist</th>\n", "      <th>num_dist</th>\n", "      <th>ros_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>172.478000</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>1267.942000</td>\n", "      <td>94.067000</td>\n", "      <td>526.933000</td>\n", "      <td>...</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>6.21000</td>\n", "      <td>0.940670</td>\n", "      <td>5.269330</td>\n", "      <td>3.065520</td>\n", "      <td>1440.420</td>\n", "      <td>79.35</td>\n", "      <td>57.25</td>\n", "      <td>13.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>5611.888267</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>7025.646733</td>\n", "      <td>3894.587931</td>\n", "      <td>3465.409069</td>\n", "      <td>...</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>73.59997</td>\n", "      <td>38.945879</td>\n", "      <td>34.654091</td>\n", "      <td>29.549249</td>\n", "      <td>12637.535</td>\n", "      <td>90.54</td>\n", "      <td>70.99</td>\n", "      <td>155.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 1xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>167.220000</td>\n", "      <td>0.000000</td>\n", "      <td>67.000000</td>\n", "      <td>...</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>0.67000</td>\n", "      <td>0.000000</td>\n", "      <td>0.670000</td>\n", "      <td>0.512330</td>\n", "      <td>167.220</td>\n", "      <td>11.49</td>\n", "      <td>6.11</td>\n", "      <td>11.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 4xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>773.330000</td>\n", "      <td>0.000000</td>\n", "      <td>312.000000</td>\n", "      <td>...</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>3.12000</td>\n", "      <td>0.000000</td>\n", "      <td>3.120000</td>\n", "      <td>1.859670</td>\n", "      <td>773.330</td>\n", "      <td>36.48</td>\n", "      <td>25.19</td>\n", "      <td>16.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>AFFLIGEM BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>2419.891000</td>\n", "      <td>0.000000</td>\n", "      <td>884.999000</td>\n", "      <td>...</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>8.84999</td>\n", "      <td>0.000000</td>\n", "      <td>8.849990</td>\n", "      <td>7.792680</td>\n", "      <td>2419.891</td>\n", "      <td>89.81</td>\n", "      <td>65.65</td>\n", "      <td>20.57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 22 columns</p>\n", "</div>"], "text/plain": ["  Market Description date_week_start date_week_end  \\\n", "0       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "1       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "2       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "3       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "4       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "\n", "                                    sku  Promo_Value  Non_Promo_Price  \\\n", "0      1664 BLONDE BOTTLE (12-15)X250ML   172.478000         2.406268   \n", "1      1664 BLONDE BOTTLE (20-24)X250ML  5611.888267         2.329011   \n", "2             8.6 ORIGINAL CAN 1xX500ML     0.000000         2.495821   \n", "3             8.6 ORIGINAL CAN 4xX500ML     0.000000         2.478622   \n", "4  AFFLIGEM BLONDE BOTTLE (12-15)X250ML     0.000000         2.734343   \n", "\n", "   Promo_Price  Non_Promo_Value  Promo_Volume  Non_Promo_Volume  ...  \\\n", "0     1.833565      1267.942000     94.067000        526.933000  ...   \n", "1     1.542802      7025.646733   3894.587931       3465.409069  ...   \n", "2          NaN       167.220000      0.000000         67.000000  ...   \n", "3          NaN       773.330000      0.000000        312.000000  ...   \n", "4          NaN      2419.891000      0.000000        884.999000  ...   \n", "\n", "   Non_Promo_Price_PPC  Promo_Price_PPC  volume_hl  promo_volume_hl  \\\n", "0             2.406268         1.833565    6.21000         0.940670   \n", "1             2.329011         1.542802   73.59997        38.945879   \n", "2             2.495821              NaN    0.67000         0.000000   \n", "3             2.478622              NaN    3.12000         0.000000   \n", "4             2.734343              NaN    8.84999         0.000000   \n", "\n", "   non_promo_volume_hl  base_volume_hl  value_eur  w_dist  num_dist  ros_value  \n", "0             5.269330        3.065520   1440.420   79.35     57.25      13.86  \n", "1            34.654091       29.549249  12637.535   90.54     70.99     155.40  \n", "2             0.670000        0.512330    167.220   11.49      6.11      11.11  \n", "3             3.120000        1.859670    773.330   36.48     25.19      16.18  \n", "4             8.849990        7.792680   2419.891   89.81     65.65      20.57  \n", "\n", "[5 rows x 22 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["niq_sku.head()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Market Description</th>\n", "      <th>date_week_start</th>\n", "      <th>date_week_end</th>\n", "      <th>sku</th>\n", "      <th>Promo_Value</th>\n", "      <th>Non_Promo_Price</th>\n", "      <th>Promo_Price</th>\n", "      <th>Non_Promo_Value</th>\n", "      <th>Promo_Volume</th>\n", "      <th>Non_Promo_Volume</th>\n", "      <th>...</th>\n", "      <th>Non_Promo_Price_PPC</th>\n", "      <th>Promo_Price_PPC</th>\n", "      <th>volume_hl</th>\n", "      <th>promo_volume_hl</th>\n", "      <th>non_promo_volume_hl</th>\n", "      <th>base_volume_hl</th>\n", "      <th>value_eur</th>\n", "      <th>w_dist</th>\n", "      <th>num_dist</th>\n", "      <th>ros_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>172.478000</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>1267.942000</td>\n", "      <td>94.067000</td>\n", "      <td>526.933000</td>\n", "      <td>...</td>\n", "      <td>2.406268</td>\n", "      <td>1.833565</td>\n", "      <td>6.21000</td>\n", "      <td>0.940670</td>\n", "      <td>5.269330</td>\n", "      <td>3.065520</td>\n", "      <td>1440.420</td>\n", "      <td>79.35</td>\n", "      <td>57.25</td>\n", "      <td>13.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>1664 BLONDE BOTTLE (20-24)X250ML</td>\n", "      <td>5611.888267</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>7025.646733</td>\n", "      <td>3894.587931</td>\n", "      <td>3465.409069</td>\n", "      <td>...</td>\n", "      <td>2.329011</td>\n", "      <td>1.542802</td>\n", "      <td>73.59997</td>\n", "      <td>38.945879</td>\n", "      <td>34.654091</td>\n", "      <td>29.549249</td>\n", "      <td>12637.535</td>\n", "      <td>90.54</td>\n", "      <td>70.99</td>\n", "      <td>155.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 1xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>167.220000</td>\n", "      <td>0.000000</td>\n", "      <td>67.000000</td>\n", "      <td>...</td>\n", "      <td>2.495821</td>\n", "      <td>NaN</td>\n", "      <td>0.67000</td>\n", "      <td>0.000000</td>\n", "      <td>0.670000</td>\n", "      <td>0.512330</td>\n", "      <td>167.220</td>\n", "      <td>11.49</td>\n", "      <td>6.11</td>\n", "      <td>11.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>8.6 ORIGINAL CAN 4xX500ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>773.330000</td>\n", "      <td>0.000000</td>\n", "      <td>312.000000</td>\n", "      <td>...</td>\n", "      <td>2.478622</td>\n", "      <td>NaN</td>\n", "      <td>3.12000</td>\n", "      <td>0.000000</td>\n", "      <td>3.120000</td>\n", "      <td>1.859670</td>\n", "      <td>773.330</td>\n", "      <td>36.48</td>\n", "      <td>25.19</td>\n", "      <td>16.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN DRIVE</td>\n", "      <td>2021-12-27</td>\n", "      <td>2022-01-02</td>\n", "      <td>AFFLIGEM BLONDE BOTTLE (12-15)X250ML</td>\n", "      <td>0.000000</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>2419.891000</td>\n", "      <td>0.000000</td>\n", "      <td>884.999000</td>\n", "      <td>...</td>\n", "      <td>2.734343</td>\n", "      <td>NaN</td>\n", "      <td>8.84999</td>\n", "      <td>0.000000</td>\n", "      <td>8.849990</td>\n", "      <td>7.792680</td>\n", "      <td>2419.891</td>\n", "      <td>89.81</td>\n", "      <td>65.65</td>\n", "      <td>20.57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 22 columns</p>\n", "</div>"], "text/plain": ["  Market Description date_week_start date_week_end  \\\n", "0       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "1       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "2       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "3       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "4       AUCHAN DRIVE      2021-12-27    2022-01-02   \n", "\n", "                                    sku  Promo_Value  Non_Promo_Price  \\\n", "0      1664 BLONDE BOTTLE (12-15)X250ML   172.478000         2.406268   \n", "1      1664 BLONDE BOTTLE (20-24)X250ML  5611.888267         2.329011   \n", "2             8.6 ORIGINAL CAN 1xX500ML     0.000000         2.495821   \n", "3             8.6 ORIGINAL CAN 4xX500ML     0.000000         2.478622   \n", "4  AFFLIGEM BLONDE BOTTLE (12-15)X250ML     0.000000         2.734343   \n", "\n", "   Promo_Price  Non_Promo_Value  Promo_Volume  Non_Promo_Volume  ...  \\\n", "0     1.833565      1267.942000     94.067000        526.933000  ...   \n", "1     1.542802      7025.646733   3894.587931       3465.409069  ...   \n", "2          NaN       167.220000      0.000000         67.000000  ...   \n", "3          NaN       773.330000      0.000000        312.000000  ...   \n", "4          NaN      2419.891000      0.000000        884.999000  ...   \n", "\n", "   Non_Promo_Price_PPC  Promo_Price_PPC  volume_hl  promo_volume_hl  \\\n", "0             2.406268         1.833565    6.21000         0.940670   \n", "1             2.329011         1.542802   73.59997        38.945879   \n", "2             2.495821              NaN    0.67000         0.000000   \n", "3             2.478622              NaN    3.12000         0.000000   \n", "4             2.734343              NaN    8.84999         0.000000   \n", "\n", "   non_promo_volume_hl  base_volume_hl  value_eur  w_dist  num_dist  ros_value  \n", "0             5.269330        3.065520   1440.420   79.35     57.25      13.86  \n", "1            34.654091       29.549249  12637.535   90.54     70.99     155.40  \n", "2             0.670000        0.512330    167.220   11.49      6.11      11.11  \n", "3             3.120000        1.859670    773.330   36.48     25.19      16.18  \n", "4             8.849990        7.792680   2419.891   89.81     65.65      20.57  \n", "\n", "[5 rows x 22 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["niq_sku.head()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# interacting_promos = final_df[['Retailer','ABI PromoID', 'ABI SKU','ABI Start', 'ABI End',  'Competitor PromoID','Competitor SKU', 'Competitor Start', 'Competitor End']]\n", "# interacting_promos = interacting_promos.merge(retailers, how='left', left_on='Retailer', right_on='a3')\n", "\n", "# niq = niq_sku.merge(niq_beer, how='left', on=['Market Description', 'date_week_start','date_week_end'])\n", "# niq = niq.rename(columns={'sku_x':'sku', 'volume_hl_x':'sku_volume_hl', 'value_eur_x':'sku_value', \n", "#                     'volume_hl_y':'beer_volume_hl', 'value_eur_y':'beer_value'})\n", "# niq = niq.drop(columns='sku_y')\n", "\n", "# # Create joined dataframe \n", "# interacting_promos = interacting_promos.merge(niq, how='left', left_on=['niq', 'ABI SKU'], right_on=['Market Description', 'sku'])\n", "\n", "# interacting_promos = interacting_promos.merge(sku_segment_mapping, how='left', left_on=['sku'], right_on=['sku'])\n", "\n", "# # Merge segment-level volume and value data\n", "# interacting_promos = interacting_promos.merge(beer_pole_renamed, how='left', \n", "#                                             left_on=['niq', 'segment', 'date_week_start', 'date_week_end'], \n", "#                                             right_on=['Market Description', 'segment', 'date_week_start', 'date_week_end'])\n", "\n", "# print(f\"Added segment data to {len(interacting_promos)} promotion records\")\n", "\n", "# # CALC<PERSON>LA<PERSON> ABI PROMO MARKET SHARES DURING PROMO WEEKS\n", "# # keep only overlapping weeks\n", "# promo_ms = interacting_promos[(interacting_promos['ABI Start']<=interacting_promos['date_week_end']) & \n", "#                    (interacting_promos['ABI End']>=interacting_promos['date_week_start'])]\n", "                   \n", "\n", "# # First, merge the ABI Rounded values into promo_ms\n", "# promo_ms = promo_ms.merge(ads_df[['ABI PromoID', 'ABI Rounded']], how='left', on='ABI PromoID')\n", "\n", "# # Keep ABI promo to competitor SKU set relationship with mapped NIQ weeks\n", "# comp_set_prorata = promo_ms[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end']].drop_duplicates()\n", "\n", "# # Calculate overlapping days per NIQ week\n", "# promo_ms = promo_ms.drop_duplicates(subset=['ABI PromoID', 'date_week_start', 'date_week_end'])\n", "# promo_ms['Overlapping Days'] = promo_ms.apply(\n", "#     lambda row: ((min(row['ABI End'], row['date_week_end']) - max(row['ABI Start'], row['date_week_start']))).days + 1, axis=1)\n", "# promo_ms['niq_pro_rata'] = promo_ms['Overlapping Days'].div(7) # percent of niq week covered by promo\n", "\n", "# # NEW PTC CALCULATION METHOD\n", "\n", "# # NEW: Calculate ABI Promo Value using YOUR corrected formula\n", "# # Promo Value = Non Promo Value * (1-Rounded)\n", "# promo_ms['ABI_Promo_Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded'])\n", "\n", "# # Convert volumes to HL if needed - use existing HL columns\n", "# promo_ms['ABI_Promo_Volume_HL'] = promo_ms['promo_volume_hl_x']  # Already in HL\n", "# promo_ms['ABI_Non_Promo_Volume_HL'] = promo_ms['non_promo_volume_hl_x']  # Already in HL\n", "\n", "# # NEW: Calculate Segment Promo Value using same formula\n", "# # For segment data, use proportional calculation based on total segment data\n", "# promo_ms['Segment_Promo_Value'] = promo_ms['segment_value'] * (1 - promo_ms['ABI Rounded'])\n", "# promo_ms['Segment_Promo_Volume_HL'] = promo_ms['segment_volume_hl']  # Already in HL\n", "\n", "# comp_set_prorata = comp_set_prorata.merge(promo_ms[['ABI PromoID', 'date_week_end','niq_pro_rata']], how='left', on=['ABI PromoID', 'date_week_end'])\n", "# comp_set_prorata = comp_set_prorata[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end', 'niq_pro_rata']].drop_duplicates()\n", "\n", "# abi_sku_promo_niq_weeks = promo_ms[['Retailer','ABI SKU', 'date_week_end']].drop_duplicates()\n", "\n", "# # CALCULATE PROMO MS AND PTC FOR ABI SKU\n", "# promo_ms['w_w_dist'] = promo_ms['niq_pro_rata'] * promo_ms['w_dist']\n", "# promo_ms['w_sku_value'] = promo_ms['niq_pro_rata'] * promo_ms['sku_value']\n", "# promo_ms['w_sku_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['sku_volume_hl']\n", "# promo_ms['w_beer_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['beer_volume_hl']\n", "# promo_ms['w_num_dist'] = promo_ms['niq_pro_rata'] * promo_ms['num_dist']\n", "\n", "# # ADD: Weighted segment data calculations\n", "# promo_ms['w_segment_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['segment_volume_hl']\n", "# promo_ms['w_segment_value'] = promo_ms['niq_pro_rata'] * promo_ms['segment_value']\n", "\n", "\n", "# promo_ms['w_ABIPromo_Value'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Value']\n", "# promo_ms['w_ABIPromo_Volume'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Volume_HL']\n", "# promo_ms['w_ABINon_Promo_Value'] = promo_ms['niq_pro_rata'] * promo_ms['Non_Promo_Value_x']\n", "# promo_ms['w_ABIpromo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABIpromo_volume_hl_x']\n", "# promo_ms['w_ABInon_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABInon_promo_volume_hl']\n", "\n", "\n", "\n", "# promo_ms['w_segment_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Value']\n", "# promo_ms['w_segment_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Volume_HL']\n", "\n", "# # Include NEW weighted values in groupby aggregation\n", "# promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({\n", "#     'date_week_end': np.min, \n", "#     'w_sku_vol_hl': np.sum, \n", "#     'w_beer_vol_hl': np.sum, \n", "#     'w_sku_value': np.sum, \n", "#     'w_w_dist': np.sum, \n", "#     'w_num_dist': np.sum,\n", "#     'niq_pro_rata': np.sum,\n", "#     'w_segment_volume_hl': np.sum,  \n", "#     'w_segment_value': np.sum,\n", "#     ######\n", "#     'w_ABIPromo_Value': np.sum,\n", "#     'w_ABIPromo_Volume': np.sum,\n", "#     'w_ABINon_Promo_Value': np.sum,\n", "#     'w_ABIpromo_volume_hl': np.sum,\n", "#     'w_ABInon_promo_volume_hl': np.sum,\n", "#     ######\n", "#     'w_ABI_promo_value': np.sum,\n", "#     'w_ABI_promo_volume_hl': np.sum,\n", "#     'w_segment_promo_value': np.sum,\n", "#     'w_segment_promo_volume_hl': np.sum,\n", "#     'ABI Rounded': 'first'\n", "# })\n", "\n", "# promo_ms['ABI MS Promo'] = promo_ms['w_sku_vol_hl'].div(promo_ms['w_beer_vol_hl'])*100\n", "# promo_ms['ABI_Promo_W_W_Distribution'] = promo_ms['w_w_dist'].div(promo_ms['niq_pro_rata'])\n", "# promo_ms['ABI_Promo_W_Num_Distribution'] = promo_ms['w_num_dist'].div(promo_ms['niq_pro_rata'])\n", "\n", "# # NEW: Calculate PTC/HL using YOUR corrected formula\n", "# # ABI Promo PTC/HL = Promo Value / Promo Volume\n", "# promo_ms['ABI Promo PTC/HL'] = promo_ms['w_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])\n", "\n", "# # NEW: Segment PTC/HL using same corrected formula\n", "# promo_ms['Segment Promo PTC/HL'] = promo_ms['w_segment_promo_value'].div(promo_ms['w_segment_promo_volume_hl'])\n", "\n", "# # Calculate index\n", "# promo_ms['ABI vs Segment PTC Index'] = promo_ms['ABI Promo PTC/HL'].div(promo_ms['Segment Promo PTC/HL']) * 100\n", "\n", "# print(\" Updated to use YOUR corrected PTC calculation formula!\")\n", "# print(\" NEW: Promo Value = Non Promo Value * (1-Rounded)\")\n", "# print(\" NEW: PTC/HL = Promo Value / Promo Volume\")\n", "\n", "# comp_set_prorata = comp_set_prorata.merge(niq,how='left', left_on=['niq', 'Competitor SKU', 'date_week_end'], right_on=['Market Description', 'sku','date_week_end'])\n", "\n", "# # CALC<PERSON>LATE COMPETITOR SET PTC DURING ABI PROMOTION\n", "# comp_set_prorata['w_sku_value'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_value']\n", "# comp_set_prorata['w_sku_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_volume_hl']\n", "# comp_set_prorata['w_beer_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['beer_volume_hl']\n", "\n", "# # ADDED: Calculate weighted distribution for competitor set\n", "# comp_set_prorata['w_w_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['w_dist']\n", "# comp_set_prorata['w_num_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['num_dist']\n", "\n", "# # ADDED: Include distribution columns in competitor groupby\n", "# comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({\n", "#     'w_sku_vol_hl': np.sum,\n", "#     'w_sku_value': np.sum, \n", "#     'w_beer_vol_hl': np.max,\n", "#     'w_w_dist': np.sum,\n", "#     'w_num_dist': np.sum,\n", "#     'niq_pro_rata': np.sum\n", "# })\n", "\n", "# comp_set_prorata['Comp Promo PTC/HL'] = comp_set_prorata['w_sku_value'].div(comp_set_prorata['w_sku_vol_hl'])\n", "\n", "# # ADDED: Calculate competitor distribution metrics\n", "# comp_set_prorata['Comp W_Distribution'] = comp_set_prorata['w_w_dist'].div(comp_set_prorata['niq_pro_rata'])\n", "# comp_set_prorata['Comp Num_Distribution'] = comp_set_prorata['w_num_dist'].div(comp_set_prorata['niq_pro_rata'])\n", "\n", "# # CALC<PERSON>LATE ABI BASE MARKET SHARES AS AVG. OF 3 PREV WEEKS (EX. PROMO ON <PERSON>IM<PERSON>AR COMPETITION OR ABI SKU)\n", "# # filter out weeks before promotion occurred\n", "# base_ms = interacting_promos.merge(promo_ms[['ABI PromoID', 'date_week_end']], how='left', left_on='ABI PromoID', right_on='ABI PromoID')\n", "# base_ms.rename(columns={'date_week_end_x':'date_week_end', 'date_week_end_y':'promo_week'}, inplace=True)\n", "\n", "# base_ms = base_ms[(base_ms['date_week_end'] < base_ms['promo_week'])] \n", "\n", "# # filter out promo weeks of same ABI SKU\n", "# base_ms = base_ms.merge(abi_sku_promo_niq_weeks.rename(columns={'date_week_end':'other_promo_weeks'}), how='left', \n", "#                         left_on=['Retailer', 'ABI SKU', 'date_week_end'], \n", "#                         right_on=['Retailer','ABI SKU', 'other_promo_weeks'])\n", "\n", "# base_ms = base_ms[base_ms['date_week_end'] != base_ms['other_promo_weeks']]\n", "\n", "# # ADDED: Include distribution columns in base_ms selection\n", "# base_ms = base_ms[['Retailer','ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'date_week_end', 'sku_volume_hl','sku_value', 'beer_volume_hl', 'w_dist', 'num_dist']].drop_duplicates()\n", "\n", "# # Select previous 3 date_week_ends per ABI PromoID only\n", "# base_ms = base_ms.sort_values(by=['ABI PromoID', 'date_week_end'], ascending=[True, False])\n", "# base_ms['rank'] = base_ms.groupby('ABI PromoID')['date_week_end'].rank(method='first', ascending=False)\n", "# base_ms = base_ms[base_ms['rank'] <= 3].drop(columns=['rank'])\n", "# base_ms['str_date'] = base_ms['date_week_end'].astype(str)\n", "\n", "# # ADDED: Include distribution columns in base_ms groupby\n", "# base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({\n", "#     'sku_volume_hl': np.sum, \n", "#     'sku_value': np.sum,\n", "#     'beer_volume_hl': np.sum, \n", "#     'w_dist': np.sum,\n", "#     'num_dist': np.sum,\n", "#     'str_date': ', '.join\n", "# })\n", "\n", "# base_ms.rename(columns={'str_date':'base_ms_weeks'}, inplace=True)\n", "# base_ms['ABI MS Base'] = base_ms['sku_volume_hl'].div(base_ms['beer_volume_hl'])*100\n", "# base_ms['ABI Base PTC/HL'] = base_ms['sku_value'].div(base_ms['sku_volume_hl'])\n", "\n", "# # ADDED: Calculate base distribution metrics (average over 3 weeks)\n", "# base_ms['ABI Base W_Distribution'] = base_ms['w_dist'].div(3)\n", "# base_ms['ABI Base Num_Distribution'] = base_ms['num_dist'].div(3)\n", "\n", "# # Merge promo ms and base ms together\n", "# promo_outcomes = promo_ms.merge(base_ms, how='left', on='ABI PromoID')\n", "\n", "# # ADDED: Include competitor distribution columns in merge\n", "# promo_outcomes = promo_outcomes.merge(comp_set_prorata[['ABI PromoID', 'Comp Promo PTC/HL', 'Comp W_Distribution', 'Comp Num_Distribution']], how='left', on='ABI PromoID')\n", "\n", "# promo_outcomes['ABI MS Promo Uplift - abs'] = promo_outcomes['ABI MS Promo'] - promo_outcomes['ABI MS Base']\n", "# promo_outcomes['ABI MS Promo Uplift - rel'] = promo_outcomes['ABI MS Promo'].div(promo_outcomes['ABI MS Base'])\n", "# promo_outcomes['ABI Promo PTC/HL Index'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['Comp Promo PTC/HL']) * 100\n", "# promo_outcomes['ABI Promo PTC vs Base'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['ABI Base PTC/HL'])\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Added segment data to 138770 promotion records\n", " Updated to use YOUR corrected PTC calculation formula!\n", " NEW: Promo Value = Non Promo Value * (1-Rounded)\n", " NEW: PTC/HL = Promo Value / Promo Volume\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:87: FutureWarning: The provided callable <function min at 0x000001F2453244C0> is currently using SeriesGroupBy.min. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"min\" instead.\n", "  promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:87: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:139: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:139: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:139: FutureWarning: The provided callable <function max at 0x000001F2453243A0> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"max\" instead.\n", "  comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created promo_ptc_agg with 364 records\n", "\n", "Results for promo: AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25\n", "                                         ABI PromoID  w_Calc_ABI_promo_value  \\\n", "0  AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BL...              10769.4296   \n", "\n", "   w_ABI_promo_volume_hl  CalcABI Promo PTC  ABI Rounded  \n", "0              16.420094         655.868931          0.3  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:178: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25548\\2904855165.py:178: FutureWarning: The provided callable <function sum at 0x000001F2452F3D00> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({\n"]}], "source": ["# FIXED: Filter out \"No interaction\" records before market share calculations\n", "# Only process records that have actual competitor interactions\n", "timing_with_interactions = final_df[final_df['Competitor SKU'].notna() & (final_df['Competitor SKU'] != '')].copy()\n", "interacting_promos = timing_with_interactions[['Retailer','ABI PromoID', 'ABI SKU','ABI Start', 'ABI End',  'Competitor PromoID','Competitor SKU', 'Competitor Start', 'Competitor End']]\n", "interacting_promos = interacting_promos.merge(retailers, how='left', left_on='Retailer', right_on='a3')\n", "\n", "niq = niq_sku.merge(niq_beer, how='left', on=['Market Description', 'date_week_start','date_week_end'])\n", "niq = niq.rename(columns={'sku_x':'sku', 'volume_hl_x':'sku_volume_hl', 'value_eur_x':'sku_value', \n", "                    'volume_hl_y':'beer_volume_hl', 'value_eur_y':'beer_value'})\n", "niq = niq.drop(columns='sku_y')\n", "\n", "# Create joined dataframe \n", "interacting_promos = interacting_promos.merge(niq, how='left', left_on=['niq', 'ABI SKU'], right_on=['Market Description', 'sku'])\n", "\n", "interacting_promos = interacting_promos.merge(sku_segment_mapping, how='left', left_on=['sku'], right_on=['sku'])\n", "\n", "# Merge segment-level volume and value data\n", "interacting_promos = interacting_promos.merge(beer_pole_renamed, how='left', \n", "                                            left_on=['niq', 'segment', 'date_week_start', 'date_week_end'], \n", "                                            right_on=['Market Description', 'segment', 'date_week_start', 'date_week_end'])\n", "\n", "print(f\"Added segment data to {len(interacting_promos)} promotion records\")\n", "\n", "# CALCULATE ABI PROMO MARKET SHARES DURING PROMO WEEKS\n", "# keep only overlapping weeks\n", "promo_ms = interacting_promos[(interacting_promos['ABI Start']<=interacting_promos['date_week_end']) & \n", "                   (interacting_promos['ABI End']>=interacting_promos['date_week_start'])]\n", "                   \n", "\n", "# First, merge the ABI Rounded values into promo_ms\n", "promo_ms = promo_ms.merge(ads_df[['ABI PromoID', 'ABI Rounded']], how='left', on='ABI PromoID')\n", "\n", "# Keep ABI promo to competitor SKU set relationship with mapped NIQ weeks\n", "comp_set_prorata = promo_ms[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end']].drop_duplicates()\n", "\n", "# Calculate overlapping days per NIQ week\n", "promo_ms = promo_ms.drop_duplicates(subset=['ABI PromoID', 'date_week_start', 'date_week_end'])\n", "promo_ms['Overlapping Days'] = promo_ms.apply(\n", "    lambda row: ((min(row['ABI End'], row['date_week_end']) - max(row['ABI Start'], row['date_week_start']))).days + 1, axis=1)\n", "promo_ms['niq_pro_rata'] = promo_ms['Overlapping Days'].div(7) # percent of niq week covered by promo\n", "\n", "\n", "\n", "# NEW: Calculate ABI Promo Value using YOUR corrected formula\n", "# Promo Value = Non Promo Value * (1-Rounded)\n", "promo_ms['ABI_Promo_Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded'])\n", "\n", "# Add new measure: Calc ABI Promo Value\n", "promo_ms['Calc ABI Promo Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded']) * 10\n", "\n", "\n", "# Convert volumes to HL if needed - use existing HL columns\n", "promo_ms['ABI_Promo_Volume_HL'] = promo_ms['promo_volume_hl_x']  # Already in HL\n", "promo_ms['ABI_Non_Promo_Volume_HL'] = promo_ms['non_promo_volume_hl_x']  # Already in HL\n", "\n", "# NEW: Calculate Segment Promo Value using same formula\n", "# For segment data, use proportional calculation based on total segment data\n", "promo_ms['Segment_Promo_Value'] = promo_ms['segment_value'] * (1 - promo_ms['ABI Rounded'])\n", "promo_ms['Segment_Promo_Volume_HL'] = promo_ms['segment_volume_hl']  # Already in HL\n", "\n", "comp_set_prorata = comp_set_prorata.merge(promo_ms[['ABI PromoID', 'date_week_end','niq_pro_rata']], how='left', on=['ABI PromoID', 'date_week_end'])\n", "comp_set_prorata = comp_set_prorata[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end', 'niq_pro_rata']].drop_duplicates()\n", "\n", "abi_sku_promo_niq_weeks = promo_ms[['Retailer','ABI SKU', 'date_week_end']].drop_duplicates()\n", "\n", "# CALCULATE PROMO MS AND PTC FOR ABI SKU\n", "promo_ms['w_w_dist'] = promo_ms['niq_pro_rata'] * promo_ms['w_dist']\n", "promo_ms['w_sku_value'] = promo_ms['niq_pro_rata'] * promo_ms['sku_value']\n", "promo_ms['w_sku_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['sku_volume_hl']\n", "promo_ms['w_beer_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['beer_volume_hl']\n", "promo_ms['w_num_dist'] = promo_ms['niq_pro_rata'] * promo_ms['num_dist']\n", "\n", "# ADD: Weighted segment data calculations\n", "promo_ms['w_segment_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['segment_volume_hl']\n", "promo_ms['w_segment_value'] = promo_ms['niq_pro_rata'] * promo_ms['segment_value']\n", "\n", "# NEW: Weighted calculations using your corrected formula\n", "promo_ms['w_ABI_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Value']\n", "promo_ms['w_ABI_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Volume_HL']\n", "promo_ms['w_segment_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Value']\n", "promo_ms['w_segment_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Volume_HL']\n", "# Add weighted calculation for new measure\n", "promo_ms['w_Calc_ABI_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Calc ABI Promo Value']\n", "\n", "\n", "# Include NEW weighted values in groupby aggregation\n", "promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({\n", "    'date_week_end': np.min, \n", "    'w_sku_vol_hl': np.sum, \n", "    'w_beer_vol_hl': np.sum, \n", "    'w_sku_value': np.sum, \n", "    'w_w_dist': np.sum, \n", "    'w_num_dist': np.sum,\n", "    'niq_pro_rata': np.sum,\n", "    'w_segment_volume_hl': np.sum,  \n", "    'w_segment_value': np.sum,\n", "    # NEW: Include your corrected formula values\n", "    'w_ABI_promo_value': np.sum,\n", "    'w_ABI_promo_volume_hl': np.sum,\n", "    'w_segment_promo_value': np.sum,\n", "    'w_segment_promo_volume_hl': np.sum,\n", "    'w_Calc_ABI_promo_value': np.sum, # Add new measure to aggregation\n", "    'ABI Rounded': 'first'\n", "})\n", "\n", "promo_ms['ABI MS Promo'] = promo_ms['w_sku_vol_hl'].div(promo_ms['w_beer_vol_hl'])*100\n", "promo_ms['ABI_Promo_W_W_Distribution'] = promo_ms['w_w_dist'].div(promo_ms['niq_pro_rata'])\n", "promo_ms['ABI_Promo_W_Num_Distribution'] = promo_ms['w_num_dist'].div(promo_ms['niq_pro_rata'])\n", "\n", "# NEW: Calculate PTC/HL using YOUR corrected formula\n", "# ABI Promo PTC/HL = Promo Value / Promo Volume\n", "promo_ms['ABI Promo PTC/HL'] = promo_ms['w_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])\n", "\n", "# NEW: Segment PTC/HL using same corrected formula\n", "promo_ms['Segment Promo PTC/HL'] = promo_ms['w_segment_promo_value'].div(promo_ms['w_segment_promo_volume_hl'])\n", "\n", "# Calculate index\n", "promo_ms['ABI vs Segment PTC Index'] = promo_ms['ABI Promo PTC/HL'].div(promo_ms['Segment Promo PTC/HL']) * 100\n", "\n", "# Calculate CalcABI Promo PTC\n", "promo_ms['CalcABI Promo PTC'] = promo_ms['w_Calc_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])\n", "\n", "print(\" Updated to use YOUR corrected PTC calculation formula!\")\n", "print(\" NEW: Promo Value = Non Promo Value * (1-Rounded)\")\n", "print(\" NEW: PTC/HL = Promo Value / Promo Volume\")\n", "\n", "comp_set_prorata = comp_set_prorata.merge(niq,how='left', left_on=['niq', 'Competitor SKU', 'date_week_end'], right_on=['Market Description', 'sku','date_week_end'])\n", "\n", "# CALCULATE COMPETITOR SET PTC DURING ABI PROMOTION\n", "comp_set_prorata['w_sku_value'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_value']\n", "comp_set_prorata['w_sku_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_volume_hl']\n", "comp_set_prorata['w_beer_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['beer_volume_hl']\n", "\n", "# ADDED: Calculate weighted distribution for competitor set\n", "comp_set_prorata['w_w_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['w_dist']\n", "comp_set_prorata['w_num_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['num_dist']\n", "\n", "# ADDED: Include distribution columns in competitor groupby\n", "comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({\n", "    'w_sku_vol_hl': np.sum,\n", "    'w_sku_value': np.sum, \n", "    'w_beer_vol_hl': np.max,\n", "    'w_w_dist': np.sum,\n", "    'w_num_dist': np.sum,\n", "    'niq_pro_rata': np.sum\n", "})\n", "\n", "comp_set_prorata['Comp Promo PTC/HL'] = comp_set_prorata['w_sku_value'].div(comp_set_prorata['w_sku_vol_hl'])\n", "\n", "# ADDED: Calculate competitor distribution metrics\n", "comp_set_prorata['Comp W_Distribution'] = comp_set_prorata['w_w_dist'].div(comp_set_prorata['niq_pro_rata'])\n", "comp_set_prorata['Comp Num_Distribution'] = comp_set_prorata['w_num_dist'].div(comp_set_prorata['niq_pro_rata'])\n", "\n", "# CALCULATE ABI BASE MARKET SHARES AS AVG. OF 3 PREV WEEKS (EX. PROMO ON <PERSON><PERSON><PERSON>AR COMPETITION OR ABI SKU)\n", "# filter out weeks before promotion occurred\n", "base_ms = interacting_promos.merge(promo_ms[['ABI PromoID', 'date_week_end']], how='left', left_on='ABI PromoID', right_on='ABI PromoID')\n", "base_ms.rename(columns={'date_week_end_x':'date_week_end', 'date_week_end_y':'promo_week'}, inplace=True)\n", "\n", "base_ms = base_ms[(base_ms['date_week_end'] < base_ms['promo_week'])] \n", "\n", "# filter out promo weeks of same ABI SKU\n", "base_ms = base_ms.merge(abi_sku_promo_niq_weeks.rename(columns={'date_week_end':'other_promo_weeks'}), how='left', \n", "                        left_on=['Retailer', 'ABI SKU', 'date_week_end'], \n", "                        right_on=['Retailer','ABI SKU', 'other_promo_weeks'])\n", "\n", "base_ms = base_ms[base_ms['date_week_end'] != base_ms['other_promo_weeks']]\n", "\n", "# ADDED: Include distribution columns in base_ms selection\n", "base_ms = base_ms[['Retailer','ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'date_week_end', 'sku_volume_hl','sku_value', 'beer_volume_hl', 'w_dist', 'num_dist']].drop_duplicates()\n", "\n", "# Select previous 3 date_week_ends per ABI PromoID only\n", "base_ms = base_ms.sort_values(by=['ABI PromoID', 'date_week_end'], ascending=[True, False])\n", "base_ms['rank'] = base_ms.groupby('ABI PromoID')['date_week_end'].rank(method='first', ascending=False)\n", "base_ms = base_ms[base_ms['rank'] <= 3].drop(columns=['rank'])\n", "base_ms['str_date'] = base_ms['date_week_end'].astype(str)\n", "\n", "# ADDED: Include distribution columns in base_ms groupby\n", "base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({\n", "    'sku_volume_hl': np.sum, \n", "    'sku_value': np.sum,\n", "    'beer_volume_hl': np.sum, \n", "    'w_dist': np.sum,\n", "    'num_dist': np.sum,\n", "    'str_date': ', '.join\n", "})\n", "\n", "base_ms.rename(columns={'str_date':'base_ms_weeks'}, inplace=True)\n", "base_ms['ABI MS Base'] = base_ms['sku_volume_hl'].div(base_ms['beer_volume_hl'])*100\n", "base_ms['ABI Base PTC/HL'] = base_ms['sku_value'].div(base_ms['sku_volume_hl'])\n", "\n", "# ADDED: Calculate base distribution metrics (average over 3 weeks)\n", "base_ms['ABI Base W_Distribution'] = base_ms['w_dist'].div(3)\n", "base_ms['ABI Base Num_Distribution'] = base_ms['num_dist'].div(3)\n", "\n", "# Merge promo ms and base ms together\n", "promo_outcomes = promo_ms.merge(base_ms, how='left', on='ABI PromoID')\n", "\n", "# ADDED: Include competitor distribution columns in merge\n", "promo_outcomes = promo_outcomes.merge(comp_set_prorata[['ABI PromoID', 'Comp Promo PTC/HL', 'Comp W_Distribution', 'Comp Num_Distribution']], how='left', on='ABI PromoID')\n", "\n", "promo_outcomes['ABI MS Promo Uplift - abs'] = promo_outcomes['ABI MS Promo'] - promo_outcomes['ABI MS Base']\n", "promo_outcomes['ABI MS Promo Uplift - rel'] = promo_outcomes['ABI MS Promo'].div(promo_outcomes['ABI MS Base'])\n", "promo_outcomes['ABI Promo PTC/HL Index'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['Comp Promo PTC/HL']) * 100\n", "promo_outcomes['ABI Promo PTC vs Base'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['ABI Base PTC/HL'])\n", "\n", "# CREATE promo_ptc_agg DataFrame\n", "promo_ptc_agg = promo_ms.groupby('ABI PromoID', as_index=False).agg({\n", "    'w_ABI_promo_value': 'sum',  # Promo_Value_x\n", "    'w_ABI_promo_volume_hl': 'sum',  # promo_volume_hl_x  \n", "    'w_segment_promo_value': 'sum',  # Promo_Value_y\n", "    'w_segment_promo_volume_hl': 'sum'  # promo_volume_hl_y\n", "})\n", "\n", "# Rename columns to match expected names\n", "promo_ptc_agg = promo_ptc_agg.rename(columns={\n", "    'w_ABI_promo_value': 'Promo_Value_x',\n", "    'w_ABI_promo_volume_hl': 'promo_volume_hl_x',\n", "    'w_segment_promo_value': 'Promo_Value_y', \n", "    'w_segment_promo_volume_hl': 'promo_volume_hl_y'\n", "})\n", "\n", "# Calculate the aggregated PTC metrics\n", "promo_ptc_agg['ABI Promo PTC Agg'] = promo_ptc_agg['Promo_Value_x'] / promo_ptc_agg['promo_volume_hl_x']\n", "promo_ptc_agg['Segment Promo PTC Agg'] = promo_ptc_agg['Promo_Value_y'] / promo_ptc_agg['promo_volume_hl_y']\n", "promo_ptc_agg['ABI vs Segment PTC Index Agg'] = promo_ptc_agg['ABI Promo PTC Agg'] / promo_ptc_agg['Segment Promo PTC Agg']\n", "\n", "print(f\"Created promo_ptc_agg with {len(promo_ptc_agg)} records\")\n", "\n", "# FIXED: Add the missing merge with promo_ptc_agg to include the aggregated PTC columns\n", "promo_outcomes = promo_outcomes.merge(promo_ptc_agg[['ABI PromoID', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']], how='left', on='ABI PromoID')\n", "\n", "\n", "# QC for specific promo ID\n", "target_promo = \"AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25\"\n", "if target_promo in promo_ms['ABI PromoID'].values:\n", "    result = promo_ms[promo_ms['ABI PromoID'] == target_promo][['ABI PromoID', 'w_Calc_ABI_promo_value', 'w_ABI_promo_volume_hl', 'CalcABI Promo PTC', 'ABI Rounded']]\n", "    print(f\"\\nResults for promo: {target_promo}\")\n", "    print(result)\n", "else:\n", "    print(f\"\\nPromo ID '{target_promo}' not found in the aggregated promo_ms dataframe.\")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ebe29bf8-16e3-441f-8bbb-34a1d9718235", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ABI PromoID</th>\n", "      <th>date_week_end</th>\n", "      <th>w_sku_vol_hl</th>\n", "      <th>w_beer_vol_hl</th>\n", "      <th>w_sku_value</th>\n", "      <th>w_w_dist</th>\n", "      <th>w_num_dist</th>\n", "      <th>niq_pro_rata</th>\n", "      <th>w_segment_volume_hl</th>\n", "      <th>w_segment_value</th>\n", "      <th>...</th>\n", "      <th>Comp Promo PTC/HL</th>\n", "      <th>Comp W_Distribution</th>\n", "      <th>Comp Num_Distribution</th>\n", "      <th>ABI MS Promo Uplift - abs</th>\n", "      <th>ABI MS Promo Uplift - rel</th>\n", "      <th>ABI Promo PTC/HL Index</th>\n", "      <th>ABI Promo PTC vs Base</th>\n", "      <th>ABI Promo PTC Agg</th>\n", "      <th>Segment Promo PTC Agg</th>\n", "      <th>ABI vs Segment PTC Index Agg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BL...</td>\n", "      <td>2022-10-23</td>\n", "      <td>22.101420</td>\n", "      <td>5151.416552</td>\n", "      <td>5584.511329</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1473.965609</td>\n", "      <td>395145.019186</td>\n", "      <td>...</td>\n", "      <td>234.184812</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.350039</td>\n", "      <td>5.431088</td>\n", "      <td>28.006467</td>\n", "      <td>0.219998</td>\n", "      <td>65.586893</td>\n", "      <td>187.658051</td>\n", "      <td>0.349502</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...</td>\n", "      <td>2022-04-10</td>\n", "      <td>90.775659</td>\n", "      <td>10198.609041</td>\n", "      <td>16314.349014</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.857143</td>\n", "      <td>2695.155282</td>\n", "      <td>701133.789886</td>\n", "      <td>...</td>\n", "      <td>235.917270</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.398072</td>\n", "      <td>1.809077</td>\n", "      <td>6.857891</td>\n", "      <td>0.079955</td>\n", "      <td>16.178950</td>\n", "      <td>182.102180</td>\n", "      <td>0.088845</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...</td>\n", "      <td>2022-09-25</td>\n", "      <td>52.615676</td>\n", "      <td>4968.800593</td>\n", "      <td>9291.272343</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1444.460105</td>\n", "      <td>384451.212871</td>\n", "      <td>...</td>\n", "      <td>267.662386</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.049790</td>\n", "      <td>115.964235</td>\n", "      <td>11.019920</td>\n", "      <td>0.100908</td>\n", "      <td>29.496180</td>\n", "      <td>186.308952</td>\n", "      <td>0.158319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...</td>\n", "      <td>2023-01-01</td>\n", "      <td>53.879992</td>\n", "      <td>10415.014691</td>\n", "      <td>12374.672600</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.285714</td>\n", "      <td>2848.156441</td>\n", "      <td>785670.354543</td>\n", "      <td>...</td>\n", "      <td>242.114171</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.426118</td>\n", "      <td>5.671705</td>\n", "      <td>22.065521</td>\n", "      <td>0.204332</td>\n", "      <td>53.423752</td>\n", "      <td>206.889185</td>\n", "      <td>0.258224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...</td>\n", "      <td>2023-03-05</td>\n", "      <td>4.118574</td>\n", "      <td>8606.701536</td>\n", "      <td>1037.658571</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.857143</td>\n", "      <td>2287.566121</td>\n", "      <td>644919.295971</td>\n", "      <td>...</td>\n", "      <td>308.520405</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-0.010034</td>\n", "      <td>0.826664</td>\n", "      <td>349.463719</td>\n", "      <td>3.617926</td>\n", "      <td>1078.166881</td>\n", "      <td>197.346648</td>\n", "      <td>5.463315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>359</th>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>2022-07-03</td>\n", "      <td>115.002784</td>\n", "      <td>5959.453135</td>\n", "      <td>33730.398314</td>\n", "      <td>182.107143</td>\n", "      <td>176.765714</td>\n", "      <td>1.857143</td>\n", "      <td>1128.636109</td>\n", "      <td>366413.495257</td>\n", "      <td>...</td>\n", "      <td>260.945625</td>\n", "      <td>72.844808</td>\n", "      <td>71.939423</td>\n", "      <td>1.000149</td>\n", "      <td>2.075885</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>243.488684</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>2023-05-07</td>\n", "      <td>107.202831</td>\n", "      <td>2312.512315</td>\n", "      <td>34315.457400</td>\n", "      <td>84.882857</td>\n", "      <td>83.460000</td>\n", "      <td>0.857143</td>\n", "      <td>503.707495</td>\n", "      <td>191443.723200</td>\n", "      <td>...</td>\n", "      <td>400.841248</td>\n", "      <td>48.365000</td>\n", "      <td>47.805000</td>\n", "      <td>3.647582</td>\n", "      <td>4.691168</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>285.051928</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>2023-05-07</td>\n", "      <td>164.352816</td>\n", "      <td>4790.848512</td>\n", "      <td>52999.557100</td>\n", "      <td>182.032857</td>\n", "      <td>178.200000</td>\n", "      <td>1.857143</td>\n", "      <td>1064.970021</td>\n", "      <td>404854.912700</td>\n", "      <td>...</td>\n", "      <td>413.675100</td>\n", "      <td>44.935000</td>\n", "      <td>44.264615</td>\n", "      <td>2.442366</td>\n", "      <td>3.471550</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>285.117119</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>2023-10-15</td>\n", "      <td>170.721407</td>\n", "      <td>4564.205555</td>\n", "      <td>56048.773014</td>\n", "      <td>180.264286</td>\n", "      <td>177.387143</td>\n", "      <td>1.857143</td>\n", "      <td>1084.608659</td>\n", "      <td>418733.178371</td>\n", "      <td>...</td>\n", "      <td>255.601178</td>\n", "      <td>95.387692</td>\n", "      <td>92.839231</td>\n", "      <td>2.864107</td>\n", "      <td>4.268283</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>289.551334</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...</td>\n", "      <td>2022-09-18</td>\n", "      <td>210.207003</td>\n", "      <td>4762.097131</td>\n", "      <td>58552.902757</td>\n", "      <td>181.322857</td>\n", "      <td>177.760000</td>\n", "      <td>1.857143</td>\n", "      <td>1156.867805</td>\n", "      <td>377597.267943</td>\n", "      <td>...</td>\n", "      <td>239.154288</td>\n", "      <td>92.960769</td>\n", "      <td>92.036154</td>\n", "      <td>3.347692</td>\n", "      <td>4.139021</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>228.477348</td>\n", "      <td>inf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>364 rows × 43 columns</p>\n", "</div>"], "text/plain": ["                                           ABI PromoID date_week_end  \\\n", "0    AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BL...    2022-10-23   \n", "1    AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...    2022-04-10   \n", "2    AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...    2022-09-25   \n", "3    AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...    2023-01-01   \n", "4    AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE_12...    2023-03-05   \n", "..                                                 ...           ...   \n", "359  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...    2022-07-03   \n", "360  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...    2023-05-07   \n", "361  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...    2023-05-07   \n", "362  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...    2023-10-15   \n", "363  SUPERMARCHES MATCH_LEFFE_LEFFE BLONDE_BIERE BL...    2022-09-18   \n", "\n", "     w_sku_vol_hl  w_beer_vol_hl   w_sku_value    w_w_dist  w_num_dist  \\\n", "0       22.101420    5151.416552   5584.511329    0.000000    0.000000   \n", "1       90.775659   10198.609041  16314.349014    0.000000    0.000000   \n", "2       52.615676    4968.800593   9291.272343    0.000000    0.000000   \n", "3       53.879992   10415.014691  12374.672600    0.000000    0.000000   \n", "4        4.118574    8606.701536   1037.658571    0.000000    0.000000   \n", "..            ...            ...           ...         ...         ...   \n", "359    115.002784    5959.453135  33730.398314  182.107143  176.765714   \n", "360    107.202831    2312.512315  34315.457400   84.882857   83.460000   \n", "361    164.352816    4790.848512  52999.557100  182.032857  178.200000   \n", "362    170.721407    4564.205555  56048.773014  180.264286  177.387143   \n", "363    210.207003    4762.097131  58552.902757  181.322857  177.760000   \n", "\n", "     niq_pro_rata  w_segment_volume_hl  w_segment_value  ...  \\\n", "0        1.000000          1473.965609    395145.019186  ...   \n", "1        1.857143          2695.155282    701133.789886  ...   \n", "2        1.000000          1444.460105    384451.212871  ...   \n", "3        2.285714          2848.156441    785670.354543  ...   \n", "4        1.857143          2287.566121    644919.295971  ...   \n", "..            ...                  ...              ...  ...   \n", "359      1.857143          1128.636109    366413.495257  ...   \n", "360      0.857143           503.707495    191443.723200  ...   \n", "361      1.857143          1064.970021    404854.912700  ...   \n", "362      1.857143          1084.608659    418733.178371  ...   \n", "363      1.857143          1156.867805    377597.267943  ...   \n", "\n", "     Comp Promo PTC/HL  Comp W_Distribution  Comp Num_Distribution  \\\n", "0           234.184812             0.000000               0.000000   \n", "1           235.917270             0.000000               0.000000   \n", "2           267.662386             0.000000               0.000000   \n", "3           242.114171             0.000000               0.000000   \n", "4           308.520405             0.000000               0.000000   \n", "..                 ...                  ...                    ...   \n", "359         260.945625            72.844808              71.939423   \n", "360         400.841248            48.365000              47.805000   \n", "361         413.675100            44.935000              44.264615   \n", "362         255.601178            95.387692              92.839231   \n", "363         239.154288            92.960769              92.036154   \n", "\n", "     ABI MS Promo Uplift - abs  ABI MS Promo Uplift - rel  \\\n", "0                     0.350039                   5.431088   \n", "1                     0.398072                   1.809077   \n", "2                     1.049790                 115.964235   \n", "3                     0.426118                   5.671705   \n", "4                    -0.010034                   0.826664   \n", "..                         ...                        ...   \n", "359                   1.000149                   2.075885   \n", "360                   3.647582                   4.691168   \n", "361                   2.442366                   3.471550   \n", "362                   2.864107                   4.268283   \n", "363                   3.347692                   4.139021   \n", "\n", "     ABI Promo PTC/HL Index  ABI Promo PTC vs Base  ABI Promo PTC Agg  \\\n", "0                 28.006467               0.219998          65.586893   \n", "1                  6.857891               0.079955          16.178950   \n", "2                 11.019920               0.100908          29.496180   \n", "3                 22.065521               0.204332          53.423752   \n", "4                349.463719               3.617926        1078.166881   \n", "..                      ...                    ...                ...   \n", "359                     inf                    inf                inf   \n", "360                     inf                    inf                inf   \n", "361                     inf                    inf                inf   \n", "362                     inf                    inf                inf   \n", "363                     inf                    inf                inf   \n", "\n", "     Segment Promo PTC Agg  ABI vs Segment PTC Index Agg  \n", "0               187.658051                      0.349502  \n", "1               182.102180                      0.088845  \n", "2               186.308952                      0.158319  \n", "3               206.889185                      0.258224  \n", "4               197.346648                      5.463315  \n", "..                     ...                           ...  \n", "359             243.488684                           inf  \n", "360             285.051928                           inf  \n", "361             285.117119                           inf  \n", "362             289.551334                           inf  \n", "363             228.477348                           inf  \n", "\n", "[364 rows x 43 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(promo_outcomes)\n", "# promo_outcomes = promo_outcomes[promo_outcomes['ABI MS Base'] != 0]\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "667ff95a-85d0-4a93-ab25-8cd7e2513a5b", "showTitle": true, "tableResultSettingsMap": {}, "title": "Combine into final ADS"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Data Before Filtering ---\n", "Total rows: 873\n", "Rows with ABI MS Base = 0: 0\n", "Rows with ABI MS Base null/NaN (includes no-interaction promos): 66\n", "\n", "No interaction records with missing/zero base MS found to remove.\n", "\n", "--- Data After Filtering ---\n", "Total rows: 873\n", "Remaining no-interaction records: 48\n", "\n", "Remaining ABI MS Base values summary (for interaction records):\n", "count    807.000000\n", "mean       0.910258\n", "std        0.730832\n", "min        0.003329\n", "25%        0.482295\n", "50%        0.768557\n", "75%        1.082914\n", "max        3.593705\n", "Name: ABI MS Base, dtype: float64\n", "\n", "Mechanic categories after merging:\n", "ABI Mechanic\n", "LV           614\n", "Immediate    123\n", "FID           84\n", "No NIP        47\n", "Multi Buy      5\n", "Name: count, dtype: int64\n"]}], "source": ["# Merge the promo outcomes, which contain metrics like ABI MS Base\n", "ads_df = ads_df.merge(promo_outcomes[['ABI PromoID', 'ABI MS Promo', 'ABI MS Base','ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel',  'base_ms_weeks',\n", "                                      'ABI Promo PTC/HL Index', 'Comp Promo PTC/HL','ABI Base W_Distribution','ABI Base Num_Distribution',\"Comp W_Distribution\",\"Comp Num_Distribution\", \"ABI_Promo_W_W_Distribution\",\"ABI_Promo_W_Num_Distribution\",\n", "                                       \"ABI Promo PTC Agg\", 'Segment Promo PTC Agg',\"ABI vs Segment PTC Index Agg\"]], how='left', on='ABI PromoID')\n", "\n", "print(\"--- Data Before Filtering ---\")\n", "print(f\"Total rows: {len(ads_df)}\")\n", "print(f\"Rows with ABI MS Base = 0: {sum(ads_df['ABI MS Base'] == 0)}\")\n", "print(f\"Rows with ABI MS Base null/NaN (includes no-interaction promos): {sum(ads_df['ABI MS Base'].isna())}\")\n", "\n", "# --- CORRECTED FILTERING LOGIC ---\n", "# The old filter incorrectly dropped all no-interaction records because their 'ABI MS Base' is NaN.\n", "# The new logic only drops records that SHOULD have a base MS (i.e. interaction records) but have a null or zero value,\n", "# while correctly preserving all true no-interaction records.\n", "\n", "# 1. Identify records that are supposed to have interactions\n", "interaction_mask = ads_df['Competitor SKU'].notna() & (ads_df['Competitor SKU'] != '')\n", "\n", "# 2. Identify records with a missing or zero base MS (potential data quality issues)\n", "missing_base_ms_mask = (ads_df['ABI MS Base'] == 0) | (ads_df['ABI MS Base'].isna())\n", "\n", "# 3. Identify the specific records to drop: INTERACTION records that have a MISSING base MS\n", "records_to_drop_mask = interaction_mask & missing_base_ms_mask\n", "records_to_drop_count = records_to_drop_mask.sum()\n", "\n", "if records_to_drop_count > 0:\n", "    print(f\"\\nIdentified and removing {records_to_drop_count} interaction records with missing/zero base MS.\")\n", "    ads_df = ads_df[~records_to_drop_mask]\n", "else:\n", "    print(\"\\nNo interaction records with missing/zero base MS found to remove.\")\n", "\n", "print(\"\\n--- Data After Filtering ---\")\n", "print(f\"Total rows: {len(ads_df)}\")\n", "print(f\"Remaining no-interaction records: {ads_df['Competitor SKU'].isna().sum()}\")\n", "\n", "print(\"\\nRemaining ABI MS Base values summary (for interaction records):\")\n", "print(ads_df[ads_df['ABI MS Base'].notna()]['ABI MS Base'].describe())\n", "\n", "\n", "# # Apply the mapping to merge mechanic categories\n", "# mechanic_mapping = {\n", "#     'LV': 'LV',\n", "#     'Multi Buy': 'LV',\n", "#     'FID': 'FID',\n", "#     'Loyalty Cards': 'FID',\n", "#     'Immediate': 'Immediate',\n", "#     'RI': 'Immediate',\n", "#     'No NIP': 'No NIP'\n", "# }\n", "# ads_df['ABI Mechanic'] = ads_df['ABI Mechanic'].map(mechanic_mapping).fillna(ads_df['ABI Mechanic'])\n", "\n", "print(\"\\nMechanic categories after merging:\")\n", "print(ads_df['ABI Mechanic'].value_counts())"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available columns in promo_outcomes:\n", "['ABI PromoID', 'date_week_end', 'w_sku_vol_hl', 'w_beer_vol_hl', 'w_sku_value', 'w_w_dist', 'w_num_dist', 'niq_pro_rata', 'w_segment_volume_hl', 'w_segment_value', 'w_ABI_promo_value', 'w_ABI_promo_volume_hl', 'w_segment_promo_value', 'w_segment_promo_volume_hl', 'w_Calc_ABI_promo_value', 'ABI Rounded', 'ABI MS Promo', 'ABI_Promo_W_W_Distribution', 'ABI_Promo_W_Num_Distribution', 'ABI Promo PTC/HL', 'Segment Promo PTC/HL', 'ABI vs Segment PTC Index', 'CalcABI Promo PTC', 'sku_volume_hl', 'sku_value', 'beer_volume_hl', 'w_dist', 'num_dist', 'base_ms_weeks', 'ABI MS Base', 'ABI Base PTC/HL', 'ABI Base W_Distribution', 'ABI Base Num_Distribution', 'Comp Promo PTC/HL', 'Comp W_Distribution', 'Comp Num_Distribution', 'ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel', 'ABI Promo PTC/HL Index', 'ABI Promo PTC vs Base', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']\n", "\n", "Shape of promo_outcomes: (364, 43)\n", "Available columns in promo_ptc_agg:\n", "['ABI PromoID', 'Promo_Value_x', 'promo_volume_hl_x', 'Promo_Value_y', 'promo_volume_hl_y', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']\n", "\n", "Shape of promo_ptc_agg: (364, 8)\n"]}], "source": ["print(\"Available columns in promo_outcomes:\")\n", "print(promo_outcomes.columns.tolist())\n", "print(f\"\\nShape of promo_outcomes: {promo_outcomes.shape}\")\n", "\n", "print(\"Available columns in promo_ptc_agg:\")\n", "print(promo_ptc_agg.columns.tolist())\n", "print(f\"\\nShape of promo_ptc_agg: {promo_ptc_agg.shape}\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "35d0529a-6c99-4623-9cc2-c661f228736e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original ABI Depth values:\n", "ABI Depth\n", "26%-30%    384\n", "21%-25%    368\n", "0.34        67\n", "<20%        48\n", "31%-33%      5\n", "Others       1\n", "Name: count, dtype: int64\n", "\n", "After merging '0.34' and 'Others' categories:\n", "ABI Depth\n", "26%-30%    384\n", "21%-25%    368\n", "34%+        68\n", "<20%        48\n", "31%-33%      5\n", "Name: count, dtype: int64\n"]}], "source": ["#Merging ABI Promo Depth Volumes\n", "\n", "print(\"Original ABI Depth values:\")\n", "print(ads_df['ABI Depth'].value_counts())\n", "\n", "# Mapping dictionary for ABI Depth category merging\n", "depth_mapping = {\n", "    '26%-30%': '26%-30%',           # Keep as is\n", "    '21%-25%': '21%-25%',           # Keep as is  \n", "    '<20%': '<20%',                 # Keep as is\n", "    '31%-33%': '31%-33%',           # Keep as is\n", "    0.34: \"34%+\",               # Merge 0.34 into 34%+\n", "    'Others': '34%+'              # Keep Others as 34%+\n", "}\n", "\n", "# Apply the mapping to merge categories\n", "ads_df['ABI Depth'] = ads_df['ABI Depth'].map(depth_mapping).fillna(ads_df['ABI Depth'])\n", "\n", "\n", "\n", "print(\"\\nAfter merging '0.34' and 'Others' categories:\")\n", "print(ads_df['ABI Depth'].value_counts())"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "25a08e77-057f-4888-981b-5accccd30fcd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Dropping Columns (Comp Start end) used for diagnostics, didnt work anyways\n", "columns_to_drop = [\n", "    'Competitor PromoID',\n", "    # \"Competitor Start\",\n", "    # \"Competitor End\",\n", "    'Timing', \n", "    'Weeks since last comp promo',\n", "    \"Weeks until next comp promo\",  \n", "    'Overlap Promos', \n", "    'Category', \n", "    'Before', \n", "    'After'\n", "]\n", "\n", "# NOTE: 'Actual Overlap Days' is NOT in the drop list - we want to keep it!\n", "ads_df = ads_df.drop(columns=columns_to_drop, errors='ignore')\n", "\n", "# Rename 'Actual Overlap Days' to 'overlapping days' as requested\n", "ads_df = ads_df.rename(columns={'Actual Overlap Days': 'overlapping days'})"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "103d71aa-bf9a-4a38-81cf-feabe6c0cb98", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# list_no_interacting = ads_df.loc[ads_df['Competitor SKU'].isnull(), 'ABI PromoID'].unique()\n", "# list_interacting = ads_df.loc[ads_df['Competitor SKU'].notnull(), 'ABI PromoID'].unique()\n", "\n", "# no_interactions_to_drop = [promo_id for promo_id in list_no_interacting if promo_id in list_interacting]\n", "\n", "# ads_df = ads_df.loc[~((ads_df['ABI PromoID'].isin(no_interactions_to_drop)) & (ads_df['Competitor SKU'].isnull()))]\n", "# # ads_df.to_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/1new_test_output_ads_v3.csv')"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["# ads_df.to_csv('ads_melted_v3.csv' if MELT_SKU_FLAG else 'ads_unmelted_v3.csv', index=False)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8be8ff3d-ee96-4e25-8ba0-d10bc2fb6a16", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# ads_df = pd.read_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/test_output_ads_v2.csv')"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "02d1730a-a00e-4fdd-aa7d-4626d115be10", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running outlier removal on the current dataset...\n", "UPLIFT OUTLIER REMOVAL\n", "Input dataset: 873 records\n", "Columns in dataset: ['Retailer', 'ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic', 'ABI Depth', 'ABI Rounded', 'Competitor SKU', 'Competitor Start', 'Competitor End', 'Competitor Coverage', 'Competitor Mechanic', 'Competitor Depth', 'Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before', 'overlapping days', 'Same week', 'Avg Temp', 'KSM', 'ABI MS Promo', 'ABI MS Base', 'ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel', 'base_ms_weeks', 'ABI Promo PTC/HL Index', 'Comp Promo PTC/HL', 'ABI Base W_Distribution', 'ABI Base Num_Distribution', 'Comp W_Distribution', 'Comp Num_Distribution', 'ABI_Promo_W_W_Distribution', 'ABI_Promo_W_Num_Distribution', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']\n", "\n", " Identifying outliers with ABI MS Promo Uplift - rel > 15.0%...\n", "Found 24 extreme outliers across all retailers\n", "\n", "--- EXTREME OUTLIER ANALYSIS (Uplift > 15.0%) ---\n", "Found 24 outlier promotions across all retailers.\n", "\n", "Details of Outlier Promotions:\n", "\n", "Outliers by retailer:\n", "Retailer\n", "AUCHAN                       18\n", "AUCHAN SM + SIMPLY MARKET     3\n", "MONOPRIX                      2\n", "CORA + RECORD                 1\n", "Name: count, dtype: int64\n", "Outlier 1:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 15.35%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-10-19 00:00:00\n", "  ABI Mechanic: FID\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 1.45\n", "  ABI MS Promo Uplift - abs: 1.36\n", "\n", "Outlier 2:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 15.35%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-10-19 00:00:00\n", "  ABI Mechanic: FID\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 1.45\n", "  ABI MS Promo Uplift - abs: 1.36\n", "\n", "Outlier 3:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 18.40%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-05-11 00:00:00\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 0.54\n", "  ABI MS Promo Uplift - abs: 0.51\n", "\n", "Outlier 4:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 18.40%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-05-11 00:00:00\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 0.54\n", "  ABI MS Promo Uplift - abs: 0.51\n", "\n", "Outlier 5:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 250.52%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-09-21 00:00:00\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.11\n", "  ABI MS Promo Uplift - abs: 2.11\n", "\n", "Outlier 6:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 250.52%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-09-21 00:00:00\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.11\n", "  ABI MS Promo Uplift - abs: 2.11\n", "\n", "Outlier 7:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 250.52%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2022-09-21 00:00:00\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.11\n", "  ABI MS Promo Uplift - abs: 2.11\n", "\n", "Outlier 8:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 130.66%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2023-02-28 00:00:00\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.34\n", "  ABI MS Promo Uplift - abs: 2.32\n", "\n", "Outlier 9:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 130.66%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2023-02-28 00:00:00\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.34\n", "  ABI MS Promo Uplift - abs: 2.32\n", "\n", "Outlier 10:\n", "  Retailer: AUCHAN\n", "  ABI MS Promo Uplift - rel: 130.66%\n", "  ABI SKU: BUD BOTTLE (12-15)X250ML\n", "  ABI Start: 2023-02-28 00:00:00\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  ABI MS Promo: 2.34\n", "  ABI MS Promo Uplift - abs: 2.32\n", "\n", "... and 14 more outliers\n", "Removing 24 outliers from dataset across all retailers...\n", "Cleaned dataset: 849 records (removed 24 outliers)\n", "\n", "SUMMARY:\n", "Original dataset: 873 records\n", "After removing missing uplift: 873 records\n", "Extreme outliers found: 24 records\n", "All outliers removed: 24 records\n", "Final cleaned dataset: 849 records\n", "\n", "Uplift statistics (before cleaning):\n", "  Mean: 5.89%\n", "  Median: 2.56%\n", "  Max: 620.08%\n", "\n", "Uplift statistics (after cleaning):\n", "  Mean: 2.93%\n", "  Median: 2.51%\n", "  Max: 11.50%\n", "\n", "Outlier removal complete! Dataset updated with 849 clean records.\n"]}], "source": ["# REMOVING OUTLIERS (anything greater than 15, will try with 10 too)\n", "def remove_extreme_uplift_outliers(df, outlier_threshold=15.0):\n", "\n", "    print(\"UPLIFT OUTLIER REMOVAL\")\n", "    df_work = df.copy()\n", "    \n", "    # basic info for my sanity check\n", "    print(f\"Input dataset: {len(df_work)} records\")\n", "    print(f\"Columns in dataset: {list(df_work.columns)}\")\n", "\n", "    uplift_column = 'ABI MS Promo Uplift - rel'\n", "    \n", "    retailer_column = 'Retailer'\n", "    original_count = len(df_work)\n", "    \n", "    # Convert uplift to numeric if it's not already\n", "    df_work[uplift_column] = pd.to_numeric(df_work[uplift_column], errors='coerce')\n", "    \n", "    # Identify extreme outliers\n", "    print(f\"\\n Identifying outliers with {uplift_column} > {outlier_threshold}%...\")\n", "    \n", "    extreme_outliers = df_work[df_work[uplift_column] > outlier_threshold].copy()\n", "    \n", "    if len(extreme_outliers) == 0:\n", "        print(f\"No outliers found with uplift > {outlier_threshold}%\")\n", "        return df_work, pd.DataFrame()\n", "    \n", "    print(f\"Found {len(extreme_outliers)} extreme outliers across all retailers\")\n", "    \n", "    # Display details of outliers being removed\n", "    if len(extreme_outliers) > 0:\n", "        print(f\"\\n--- EXTREME OUTLIER ANALYSIS (Uplift > {outlier_threshold}%) ---\")\n", "        print(f\"Found {len(extreme_outliers)} outlier promotions across all retailers.\")\n", "        print(\"\\nDetails of Outlier Promotions:\")\n", "        \n", "        # Show breakdown by retailer\n", "        print(\"\\nOutliers by retailer:\")\n", "        print(extreme_outliers[retailer_column].value_counts())\n", "        \n", "        relevant_cols = [retailer_column, uplift_column]\n", "        \n", "        # Add other columns if they exist\n", "        optional_cols = [\n", "            'ABI SKU', 'ABI_SKU', 'SKU', 'sku',\n", "            'ABI Start', 'ABI_Start', 'start_date', 'Start_Date',\n", "            'ABI Mechanic', 'ABI_Mechanic', 'mechanic', 'Mechanic',\n", "            'ABI Depth', 'ABI_Depth', 'depth', 'Depth',\n", "            'ABI MS Promo', 'ABI_MS_Promo', 'ms_promo',\n", "            'ABI MS Promo Uplift - abs', 'ABI_MS_Promo_Uplift_abs',\n", "            'Avg Temp', 'Avg_Temp', 'temperature'\n", "        ]\n", "        \n", "        for col in optional_cols:\n", "            if col in df_work.columns:\n", "                relevant_cols.append(col)\n", "        \n", "        # Remove duplicates while preserving order\n", "        relevant_cols = list(dict.fromkeys(relevant_cols))\n", "        \n", "        # Display outlier details (limit to first 10 for readability)\n", "        display_cols = [col for col in relevant_cols if col in extreme_outliers.columns][:8]  # Limit to 8 cols for readability\n", "        \n", "        display_limit = min(10, len(extreme_outliers))  # Show max 10 outliers\n", "        for idx, (_, row) in enumerate(extreme_outliers.head(display_limit).iterrows(), 1):\n", "            print(f\"Outlier {idx}:\")\n", "            for col in display_cols:\n", "                value = row[col]\n", "                if pd.isna(value):\n", "                    value = \"N/A\"\n", "                elif isinstance(value, float):\n", "                    if col == uplift_column:\n", "                        value = f\"{value:.2f}%\"\n", "                    else:\n", "                        value = f\"{value:.2f}\"\n", "                print(f\"  {col}: {value}\")\n", "            print()\n", "        \n", "        if len(extreme_outliers) > display_limit:\n", "            print(f\"... and {len(extreme_outliers) - display_limit} more outliers\")\n", "    \n", "    print(f\"Removing {len(extreme_outliers)} outliers from dataset across all retailers...\")\n", "    \n", "    cleaned_df = df_work.drop(extreme_outliers.index).copy()\n", "    \n", "    print(f\"Cleaned dataset: {len(cleaned_df)} records (removed {len(extreme_outliers)} outliers)\")\n", "    \n", "    # Summary statistics\n", "    print(f\"\\nSUMMARY:\")\n", "    print(f\"Original dataset: {original_count} records\")\n", "    print(f\"After removing missing uplift: {len(df_work)} records\")\n", "    print(f\"Extreme outliers found: {len(extreme_outliers)} records\")\n", "    print(f\"All outliers removed: {len(extreme_outliers)} records\")\n", "    print(f\"Final cleaned dataset: {len(cleaned_df)} records\")\n", "    \n", "    if len(df_work) > 0:\n", "        print(f\"\\nUplift statistics (before cleaning):\")\n", "        print(f\"  Mean: {df_work[uplift_column].mean():.2f}%\")\n", "        print(f\"  Median: {df_work[uplift_column].median():.2f}%\")\n", "        print(f\"  Max: {df_work[uplift_column].max():.2f}%\")\n", "        \n", "        if len(cleaned_df) > 0:\n", "            print(f\"\\nUplift statistics (after cleaning):\")\n", "            print(f\"  Mean: {cleaned_df[uplift_column].mean():.2f}%\")\n", "            print(f\"  Median: {cleaned_df[uplift_column].median():.2f}%\")\n", "            print(f\"  Max: {cleaned_df[uplift_column].max():.2f}%\")\n", "    \n", "    return cleaned_df, extreme_outliers\n", "\n", "# Run the outlier removal on the loaded dataset\n", "print(\"Running outlier removal on the current dataset...\")\n", "df_cleaned, outliers_removed = remove_extreme_uplift_outliers(ads_df, outlier_threshold=15.0)\n", "\n", "# Update the main dataframe with cleaned data\n", "ads_df = df_cleaned.copy()\n", "\n", "print(f\"\\nOutlier removal complete! Dataset updated with {len(ads_df)} clean records.\")"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3c7d0488-ec42-4dad-830d-7942a5094bb2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ads_df.to_csv('ads_melted_v3.csv' if MELT_SKU_FLAG else 'ads_unmelted_v3.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5414d08f-eec9-465a-9fc2-60e74553dd49", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# # Final ADS Export\n", "# ads_df.to_csv('/dbfs/FileStore/RevMan/1emock_ads.csv')\n"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a062bf76-097a-4a6c-8497-c02a54c1f547", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# # Load the CSV file into a DataFrame\n", "# ads = spark.read.csv('dbfs:/mnt/b2b/RevMan/PromoCalendar/1mock_test_output_ads_v2.csv', header=True, inferSchema=True)\n", "\n", "# # Display the DataFrame\n", "# display(ads)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "[ACTIVE] On New Data PromoCalendarPlanner", "widgets": {}}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 0}
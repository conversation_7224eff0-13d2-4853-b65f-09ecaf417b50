import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Cell 2: Analysis for new_test_output_ads_v3.csv

# Load the dataset
try:
    df2 = pd.read_csv('new_test_output_ads_v3.csv')
except FileNotFoundError as e:
    print(e)
    print("Please ensure 'new_test_output_ads_v3.csv' is in the same directory as the notebook.")
    raise

# Define the timing columns
timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

def get_timing_category(row):
    """Assign a timing category to a row based on boolean flags."""
    for col in timing_cols:
        if row[col]:
            return col
    return 'No Overlap'

# Apply the function to create the 'timing_category' column
df2['timing_category'] = df2.apply(get_timing_category, axis=1)

# Set up the plot
plt.figure(figsize=(12, 8))
sns.boxplot(
    x='timing_category',
    y='ABI MS Promo Uplift - rel',
    data=df2,
    order=timing_cols + ['No Overlap']
)

# Add plot titles and labels
plt.title('Impact of Promotion Timing on Sales Uplift (New Test Output)', fontsize=16)
plt.xlabel('Competitor Promotion Timing', fontsize=12)
plt.ylabel('Relative Sales Uplift', fontsize=12)
plt.xticks(rotation=45)
plt.grid(True, which='both', linestyle='--', linewidth=0.5)
plt.tight_layout()

# Show the plot
plt.show()

# Display the value counts for each category
print("Value counts for New Test Output data:")
print(df2['timing_category'].value_counts()) 
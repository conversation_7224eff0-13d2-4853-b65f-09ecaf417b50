STDOUT:
A3 shape after filtering: (18935, 47)
Segment distribution:
segment
PREMIUM SUPER PREMIUM LAGERS    10
ABBEY BEER STYLES                8
CORE LAGERS                      3
FLAVOURED                        2
Name: count, dtype: int64

Beer pole data: 8356 records
Segments in beer pole: 4

First few beer pole records:
  Market Description date_week_start  ... segment_volume_hl  segment_value
0       AUCHAN DRIVE      2021-12-27  ...        446.608270  117118.416284
1       AUCHAN DRIVE      2021-12-27  ...         65.409692   12808.020136
2       AUCHAN DRIVE      2021-12-27  ...        125.748200   43360.152000
3       AUCHAN DRIVE      2021-12-27  ...        225.173350   50658.241944
4       AUCHAN DRIVE      2022-01-03  ...        322.837824   89904.235252

[5 rows x 6 columns]


STDERR:
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:151: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:151: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:151: FutureWarning: The provided callable <function max at 0x00000280FFBCA050> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "max" instead.
  niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:183: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:183: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:183: FutureWarning: The provided callable <function max at 0x00000280FFBCA050> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "max" instead.
  niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:209: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:209: FutureWarning: The provided callable <function sum at 0x00000280FFBC9A20> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "sum" instead.
  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({
C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py:209: FutureWarning: The provided callable <function max at 0x00000280FFBCA050> is currently using SeriesGroupBy.max. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string "max" instead.
  beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Swagat\ADS V3\old_timing_interaction\KSM 1 wk\current_notebook_script.py", line 305, in <module>
    display(niq_sku)
NameError: name 'display' is not defined


Return code: 1
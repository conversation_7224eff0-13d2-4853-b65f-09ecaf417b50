#!/usr/bin/env python3
"""
Run the notebook with both MELT_SKU_FLAG settings to compare results
"""

import subprocess
import sys
import os

def run_notebook_with_flag(flag_value):
    """Run the notebook with the specified MELT_SKU_FLAG value"""
    
    print(f"\n{'='*60}")
    print(f"RUNNING WITH MELT_SKU_FLAG = {flag_value}")
    print(f"{'='*60}")
    
    # Read the notebook
    with open('Trying_with_Switch_PromoCalendarPlanner.ipynb', 'r', encoding='utf-8') as f:
        notebook_content = f.read()
    
    # Replace the flag value
    if flag_value:
        new_content = notebook_content.replace('MELT_SKU_FLAG = False', 'MELT_SKU_FLAG = True')
    else:
        new_content = notebook_content.replace('MELT_SKU_FLAG = True', 'MELT_SKU_FLAG = False')
    
    # Write the modified notebook
    temp_notebook = f'temp_notebook_{str(flag_value).lower()}.ipynb'
    with open(temp_notebook, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    try:
        # Convert to Python script
        script_name = f'temp_script_{str(flag_value).lower()}.py'
        cmd = f'jupyter nbconvert --to python {temp_notebook} --output {script_name.replace(".py", "")}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error converting notebook: {result.stderr}")
            return None
        
        # Run the script
        output_file = f'output_{str(flag_value).lower()}.txt'
        cmd = f'python {script_name} > {output_file} 2>&1'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        print(f"Script execution completed. Output saved to {output_file}")
        
        # Read and display key parts of the output
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                output = f.read()
            
            # Look for key information
            lines = output.split('\n')
            for i, line in enumerate(lines):
                if 'MELT_SKU_FLAG VERIFICATION' in line or 'UNMELTED MODE AGGREGATION' in line or 'MELTED MODE' in line:
                    # Print this line and the next 20 lines
                    for j in range(i, min(i+20, len(lines))):
                        print(lines[j])
                    break
        
        # Check if CSV was created
        csv_name = 'ads_melted_v3.csv' if flag_value else 'ads_unmelted_v3.csv'
        if os.path.exists(csv_name):
            # Count lines in CSV
            with open(csv_name, 'r') as f:
                line_count = sum(1 for line in f) - 1  # Subtract header
            print(f"\nCSV created: {csv_name} with {line_count} rows")
            return line_count
        else:
            print(f"\nCSV not found: {csv_name}")
            return None
            
    except Exception as e:
        print(f"Error running script: {e}")
        return None
    
    finally:
        # Clean up temp files
        for temp_file in [temp_notebook, script_name]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

def main():
    print("Running notebook with both MELT_SKU_FLAG settings...")
    
    # Run with both settings
    melted_rows = run_notebook_with_flag(True)
    unmelted_rows = run_notebook_with_flag(False)
    
    # Compare results
    print(f"\n{'='*60}")
    print("FINAL COMPARISON")
    print(f"{'='*60}")
    
    if melted_rows is not None and unmelted_rows is not None:
        print(f"MELTED mode rows:   {melted_rows}")
        print(f"UNMELTED mode rows: {unmelted_rows}")
        print(f"Difference:         {melted_rows - unmelted_rows}")
        
        if melted_rows > unmelted_rows:
            print("✅ SUCCESS: UNMELTED mode has fewer rows (aggregation worked)")
        elif melted_rows == unmelted_rows:
            print("⚠️  WARNING: Both modes have same row count (no aggregation occurred)")
        else:
            print("❌ ERROR: MELTED mode has fewer rows (unexpected)")
    else:
        print("❌ Could not determine row counts from both runs")

if __name__ == "__main__":
    main()

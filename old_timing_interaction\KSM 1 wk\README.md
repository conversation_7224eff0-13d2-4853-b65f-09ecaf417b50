# Promotional Analysis Pipeline

A modular Python system for analyzing promotional effectiveness and market share impact of beer promotions.

## Overview

This system processes promotional calendar data to analyze:
- Timing interactions between ABI (Anheuser-Busch InBev) and competitor promotions
- Market share impacts during promotional periods
- Price-to-consumer (PTC) metrics and promotional effectiveness
- Distribution metrics and competitive positioning

## Project Structure

```
old_timing_interaction/
├── src/                        # Source code modules
│   ├── config.py              # Configuration settings and constants
│   ├── data_loader.py         # Data loading and preprocessing
│   ├── timing_calculator.py   # Promotional timing calculations
│   ├── data_processor.py      # Main data processing pipeline
│   ├── market_share_calculator.py  # Market share and PTC calculations
│   ├── validation.py          # Data validation utilities
│   └── main.py               # Main orchestration script
├── data files/               # Input data
│   ├── a3_distribution_march_2025_peter_wf.xlsx
│   ├── latest_niq_extract.xlsx
│   ├── Latest_Similar_SKU_Mapping.csv
│   ├── Retailers.csv
│   ├── Weather.csv
│   └── Holidays.csv
├── test_modular_pipeline.py  # Test script
└── README.md                 # This file
```

## Installation

1. Ensure Python 3.8+ is installed
2. Install required dependencies:

```bash
pip install pandas numpy openpyxl
```

## Usage

### Basic Usage

Run the complete promotional analysis pipeline:

```bash
python src/main.py
```

### Command Line Options

```bash
# Skip validation against original output
python src/main.py --no-validate

# Specify custom output file
python src/main.py --output custom_output.csv
```

### Test Script

Run the test script to verify the pipeline:

```bash
python test_modular_pipeline.py
```

## Module Documentation

### config.py
Contains all configuration settings:
- `DataPaths`: File paths for all input/output files
- `ProcessingConfig`: Processing parameters (date filters, thresholds, mappings)
- `LoggingConfig`: Logging configuration
- `ColumnNames`: Standardized column name constants

### data_loader.py
Handles all data loading operations:
- `DataLoader`: Main class for loading and preprocessing data
  - `load_a3_data()`: Loads A3 promotional calendar
  - `load_niq_data()`: Loads NIQ market data (returns 5 DataFrames)
  - `load_reference_data()`: Loads supporting data files

### timing_calculator.py
Calculates timing interactions between promotions:
- `TimingCalculator`: Analyzes promotional timing
  - `calculate_promo_interactions()`: Main timing calculation
  - `create_promo_granularity()`: Creates unique promotion identifiers

### market_share_calculator.py
Calculates market share and effectiveness metrics:
- `MarketShareCalculator`: Computes promotional outcomes
  - `calculate_promotional_outcomes()`: Main calculation method
  - Calculates promo/base market shares, PTC metrics, uplift

### data_processor.py
Main pipeline orchestration:
- `DataProcessor`: Coordinates the entire analysis
  - `process_promotional_data()`: Executes complete pipeline
  - Manages data flow between modules
  - Applies quality filters and mappings

### validation.py
Data quality and validation tools:
- `DataValidator`: Validates output consistency
  - `validate_output_consistency()`: Compares outputs
  - `create_validation_report()`: Generates validation report

## Data Flow

1. **Data Loading**: Load A3 promotions, NIQ market data, and reference files
2. **Timing Calculation**: Identify promotional overlaps and timing relationships
3. **ADS Creation**: Create Analytical Dataset with timing features
4. **Market Share Calculation**: Calculate promotional and base market shares
5. **Outcome Analysis**: Compute uplift metrics and effectiveness measures
6. **Validation**: Verify output consistency and quality

## Key Features

- **Modular Design**: Each component handles a specific aspect of the analysis
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Data Validation**: Built-in validation against expected outputs
- **Flexible Configuration**: Easy to modify parameters and mappings
- **Error Handling**: Robust error handling throughout the pipeline

## Output

The pipeline generates a CSV file containing:
- Promotional details (retailer, SKU, dates, mechanics)
- Timing interactions with competitors
- Market share metrics (promo, base, uplift)
- PTC indices and distribution metrics
- Weather and holiday indicators

## Troubleshooting

If you encounter issues:

1. Check log files for detailed error messages
2. Verify all input files are present in the correct format
3. Ensure date formats match expected patterns
4. Review configuration settings in `config.py`

## Future Enhancements

Potential improvements:
- Database integration for data storage
- Web interface for result visualization
- Automated report generation
- Machine learning for uplift prediction
- Real-time promotional monitoring 
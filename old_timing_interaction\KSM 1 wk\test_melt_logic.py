#!/usr/bin/env python3
"""
Test script to verify the MELT_SKU_FLAG logic works correctly.
This script will run the core melting logic with both settings and show the results.
"""

import pandas as pd
import numpy as np
import sys
import os

def test_melt_logic(melt_flag_value):
    """Test the melting logic with the given flag value"""
    
    print(f"\n{'='*60}")
    print(f"TESTING WITH MELT_SKU_FLAG = {melt_flag_value}")
    print(f"{'='*60}")
    
    # Set the flag
    MELT_SKU_FLAG = melt_flag_value
    
    try:
        # Load the data files
        print("Loading data files...")
        
        # Load A3 data
        a3_path = 'a3_distribution_march_2025_peter_wf.xlsx'
        if not os.path.exists(a3_path):
            print(f"ERROR: {a3_path} not found!")
            return None
            
        a3_df = pd.read_excel(a3_path, engine='openpyxl', sheet_name='a3_distribution_march_2025')
        print(f"A3 data loaded: {a3_df.shape}")
        
        # Load NIQ data
        niq_path = 'latest_niq_extract.xlsx'
        if not os.path.exists(niq_path):
            print(f"ERROR: {niq_path} not found!")
            return None
            
        niq = pd.read_excel(niq_path, engine='openpyxl')
        print(f"NIQ data loaded: {niq.shape}")
        
        # Load similar SKUs
        similar_skus_path = 'Latest_Similar_SKU_Mapping.csv'
        if not os.path.exists(similar_skus_path):
            print(f"ERROR: {similar_skus_path} not found!")
            return None
            
        similar_skus = pd.read_csv(similar_skus_path)
        print(f"Similar SKUs loaded: {similar_skus.shape}")
        
        # Basic preprocessing (simplified)
        print("Preprocessing data...")
        
        # Filter A3 data
        a3_df['Date de début'] = pd.to_datetime(a3_df['Date de début'], dayfirst=True, errors='coerce')
        a3_df = a3_df[a3_df['Date de début'] >= pd.Timestamp("2022-04-01")]
        a3_df.rename(columns={'Similar SKU Mapping': 'Similar_SKU_Mapping'}, inplace=True)
        
        # Create promo dataframe
        promo = a3_df.groupby(['promo_id', 'Similar_SKU_Mapping', 'Enseigne', 'Date de début', 'Date de fin'], 
                             as_index=False, dropna=False).agg({
            'Rounded': 'max',
            'Bucket': 'first',
            'MECHA': 'first',
            'Dn OP': 'max'
        })
        
        print(f"Promo data created: {promo.shape}")
        
        # Create a simple test dataset to demonstrate the melting logic
        print("Creating test interaction data...")
        
        # Simulate some interaction records
        test_records = [
            {
                'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
                'Competitor SKU': 'COMP_SKU_1', 'Competitor PromoID': 'COMP_001',
                'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
                'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
                'Competitor Start': pd.Timestamp('2022-05-02'), 'Competitor End': pd.Timestamp('2022-05-08'),
                'Competitor Coverage': 80, 'Competitor Depth': 15, 'Timing': 'Overlap',
                'Weeks since last comp promo': 2, 'Weeks until next comp promo': 3,
                'Overlap Promos': 1, 'Actual Overlap Days': 6, 'Category': 'Interaction'
            },
            {
                'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
                'Competitor SKU': 'COMP_SKU_1', 'Competitor PromoID': 'COMP_002',  # Same comp SKU, different promo
                'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
                'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
                'Competitor Start': pd.Timestamp('2022-05-03'), 'Competitor End': pd.Timestamp('2022-05-09'),
                'Competitor Coverage': 75, 'Competitor Depth': 18, 'Timing': 'Overlap',
                'Weeks since last comp promo': 1, 'Weeks until next comp promo': 4,
                'Overlap Promos': 1, 'Actual Overlap Days': 5, 'Category': 'Interaction'
            },
            {
                'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
                'Competitor SKU': 'COMP_SKU_2', 'Competitor PromoID': 'COMP_003',  # Different comp SKU
                'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
                'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
                'Competitor Start': pd.Timestamp('2022-05-04'), 'Competitor End': pd.Timestamp('2022-05-10'),
                'Competitor Coverage': 90, 'Competitor Depth': 25, 'Timing': 'Overlap',
                'Weeks since last comp promo': 0, 'Weeks until next comp promo': 2,
                'Overlap Promos': 1, 'Actual Overlap Days': 4, 'Category': 'Interaction'
            }
        ]
        
        # Add some non-interaction records
        no_interaction_records = [
            {
                'Retailer': 'RETAILER_B', 'ABI PromoID': 'ABI_002', 'ABI SKU': 'ABI_SKU_Y',
                'Competitor SKU': '', 'Competitor PromoID': 'No interaction',
                'ABI Start': pd.Timestamp('2022-06-01'), 'ABI End': pd.Timestamp('2022-06-07'),
                'ABI Coverage': 100, 'ABI Mechanic': 'FID', 'ABI Depth': 15, 'ABI Rounded': 15,
                'Competitor Start': pd.NaT, 'Competitor End': pd.NaT,
                'Competitor Coverage': np.nan, 'Competitor Depth': np.nan, 'Timing': np.nan,
                'Weeks since last comp promo': 999, 'Weeks until next comp promo': -999,
                'Overlap Promos': 0, 'Actual Overlap Days': 0, 'Category': 'No interaction'
            }
        ]
        
        # Create the final dataframe
        final_df = pd.DataFrame(test_records + no_interaction_records)
        
        print(f"Test data created: {len(final_df)} rows")
        print(f"- Interaction records: {len(test_records)}")
        print(f"- No-interaction records: {len(no_interaction_records)}")
        
        # Apply the melting logic
        print(f"\nApplying MELT_SKU_FLAG = {MELT_SKU_FLAG} logic...")
        
        if not MELT_SKU_FLAG:
            print(f'Before aggregation: {len(final_df)} rows')
            interaction_mask = (final_df['Competitor SKU'].notna()) & (final_df['Competitor SKU'] != '')
            int_df = final_df[interaction_mask]
            non_int_df = final_df[~interaction_mask]
            print(f'Interaction rows to aggregate: {len(int_df)}')
            print(f'Non-interaction rows (unchanged): {len(non_int_df)}')

            # Group by ABI promo details INCLUDING Competitor SKU
            group_cols = ['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU',
                          'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic',
                          'ABI Depth', 'ABI Rounded']

            agg_dict = {
                'Competitor PromoID': lambda x: '|'.join(sorted(set(x.dropna().astype(str)))),
                'Competitor Start': 'min',
                'Competitor End': 'max',
                'Competitor Coverage': 'mean',
                'Competitor Depth': 'first',
                'Timing': 'first',
                'Weeks since last comp promo': 'first',
                'Weeks until next comp promo': 'first',
                'Overlap Promos': 'max',
                'Actual Overlap Days': 'max',
                'Category': 'first'
            }
            
            int_df_aggregated = int_df.groupby(group_cols, as_index=False).agg(agg_dict)
            print(f'After aggregation: {len(int_df_aggregated)} interaction rows')
            print(f'Rows reduced by: {len(int_df) - len(int_df_aggregated)} (same comp SKU with multiple promos)')
            final_df = pd.concat([int_df_aggregated, non_int_df], ignore_index=True)
            print(f'Final total rows: {len(final_df)}')
        else:
            print(f'Melted mode: {len(final_df)} rows (no aggregation)')
        
        # Show results
        print(f"\n=== RESULTS FOR MELT_SKU_FLAG = {MELT_SKU_FLAG} ===")
        print(f"Total rows: {len(final_df)}")
        print(f"Unique ABI PromoIDs: {final_df['ABI PromoID'].nunique()}")
        print(f"Unique Competitor SKUs: {final_df[final_df['Competitor SKU'].notna() & (final_df['Competitor SKU'] != '')]['Competitor SKU'].nunique()}")
        
        # Show the actual data
        print(f"\nFinal DataFrame:")
        print(final_df[['ABI PromoID', 'Competitor SKU', 'Competitor PromoID', 'Category']].to_string())
        
        if not MELT_SKU_FLAG:
            # Check for pipe-joined promo IDs
            pipe_joined_promos = final_df['Competitor PromoID'].str.contains('\\|', na=False).sum()
            print(f"\nRows with pipe-joined Competitor PromoIDs: {pipe_joined_promos}")
            if pipe_joined_promos > 0:
                merged_rows = final_df[final_df['Competitor PromoID'].str.contains('\\|', na=False)]
                print("Merged rows:")
                for _, row in merged_rows.iterrows():
                    print(f"  Comp SKU: {row['Competitor SKU']} | Promo IDs: {row['Competitor PromoID']}")
        
        return final_df
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("Testing MELT_SKU_FLAG logic...")
    
    # Test both modes
    result_melted = test_melt_logic(True)
    result_unmelted = test_melt_logic(False)
    
    if result_melted is not None and result_unmelted is not None:
        print(f"\n{'='*60}")
        print("COMPARISON SUMMARY")
        print(f"{'='*60}")
        print(f"MELTED mode rows:   {len(result_melted)}")
        print(f"UNMELTED mode rows: {len(result_unmelted)}")
        print(f"Difference:         {len(result_melted) - len(result_unmelted)}")
        
        if len(result_melted) > len(result_unmelted):
            print("✅ SUCCESS: UNMELTED mode has fewer rows (as expected)")
        elif len(result_melted) == len(result_unmelted):
            print("⚠️  WARNING: Both modes have same row count (check if there are cases to merge)")
        else:
            print("❌ ERROR: MELTED mode has fewer rows (unexpected)")
    else:
        print("❌ Test failed due to errors")

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the dataset from the specified path
try:
    df = pd.read_csv('KSM_Merged_ads_v3_cleaned.csv')
    
    # --- Data Processing ---

    # Define the columns that represent the different timing categories
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

    # Unpivot the DataFrame to correctly handle multiple timing categories for a single promotion
    melted_df = df.melt(
        id_vars=['ABI MS Promo Uplift - rel'], 
        value_vars=timing_cols,
        var_name='Timing',
        value_name='Is_Present'
    )

    # Filter for rows where a timing category is actually present (value is 1 or True)
    present_timings = melted_df[melted_df['Is_Present'] == 1].copy()

    # Convert the relative uplift to a percentage for plotting (without multiplying by 100)
    present_timings['Uplift_Percentage'] = present_timings['ABI MS Promo Uplift - rel']

    # Calculate the average uplift for each timing category
    uplift_by_timing = present_timings.groupby('Timing')['Uplift_Percentage'].mean()
    
    # *** CORRECTED LINE ***
    # Rename the index labels for better readability on the plot
    uplift_by_timing.index = uplift_by_timing.index.str.replace(' wk ', ' Week ').str.replace('after', 'After').str.replace('before', 'Before')

    # Calculate the overall average uplift from the complete, original data (without multiplying by 100)
    overall_average = df['ABI MS Promo Uplift - rel'].mean()

    # --- Plotting ---

    # Set the visual style for the plot
    sns.set_style("darkgrid")

    # Define the desired display order for the x-axis to match the example image
    plot_order = ['Same Week', '2 Week After', '1 Week After', '1 Week Before', '2 Week Before']
    
    # Reindex the data to ensure all categories are present for plotting, even if some are empty
    uplift_by_timing = uplift_by_timing.reindex(plot_order)

    # Define colors based on whether the performance is above or below the overall average
    colors = ['#5A9C6E' if val >= overall_average else '#D44C5C' for val in uplift_by_timing]

    # Create the plot
    plt.figure(figsize=(10, 6))
    barplot = sns.barplot(x=uplift_by_timing.index, y=uplift_by_timing.values, palette=colors)

    # Add the horizontal line representing the overall average
    plt.axhline(y=overall_average, color='#E5A941', linestyle='--', linewidth=2)

    # Add a text label for the overall average line
    plt.text(len(uplift_by_timing) - 1, overall_average, f' Overall Average: {overall_average:.3f}', 
             verticalalignment='center', horizontalalignment='right', color='black', fontsize=11, weight='bold')

    # Add the percentage value on top of each bar, ensuring to handle potential missing data
    for index, value in enumerate(uplift_by_timing):
        if pd.notna(value):
            plt.text(index, value + 0.05, f'{value:.2f}', 
                     ha='center', va='bottom', fontsize=12, weight='bold')

    # Set titles and labels for clarity
    plt.title('Relative Uplift Performance by Timing\n(Green = Above Average, Red = Below Average)', fontsize=16, weight='bold')
    plt.ylabel('Relative Uplift', fontsize=12)
    plt.xlabel(None)

    # Adjust plot formatting for a clean appearance
    plt.xticks(rotation=45, ha='right')
    # Set a dynamic y-axis limit, with a default if no data is plottable
    y_max = uplift_by_timing.max() if uplift_by_timing.notna().any() else 0
    overall_max = max(y_max, overall_average if pd.notna(overall_average) else 0)
    plt.ylim(0, overall_max * 1.15 if overall_max > 0 else 3.5)
    plt.tight_layout()

    # Display the final plot
    plt.show()

except FileNotFoundError:
    print("File not found. Please ensure the path 'Visuals/KSM_Merged_ads_v3_cleaned.csv' is correct.")
except Exception as e:
    print(f"An error occurred: {e}")


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the dataset from the specified path
try:
    df = pd.read_csv('new_test_output_ads_v3.csv')
    
    # --- Data Processing ---

    # Define the columns that represent the different timing categories
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

    # Unpivot the DataFrame to correctly handle multiple timing categories for a single promotion
    melted_df = df.melt(
        id_vars=['ABI MS Promo Uplift - rel'], 
        value_vars=timing_cols,
        var_name='Timing',
        value_name='Is_Present'
    )

    # Filter for rows where a timing category is actually present (value is 1 or True)
    present_timings = melted_df[melted_df['Is_Present'] == 1].copy()

    # Convert the relative uplift to a percentage for plotting
    present_timings['Uplift_Percentage'] = present_timings['ABI MS Promo Uplift - rel'] * 100

    # Calculate the average uplift for each timing category
    uplift_by_timing = present_timings.groupby('Timing')['Uplift_Percentage'].mean()
    
    # *** CORRECTED LINE ***
    # Rename the index labels for better readability on the plot
    uplift_by_timing.index = uplift_by_timing.index.str.replace(' wk ', ' Week ').str.replace('after', 'After').str.replace('before', 'Before')

    # Calculate the overall average uplift from the complete, original data
    overall_average = df['ABI MS Promo Uplift - rel'].mean() * 100

    # --- Plotting ---

    # Set the visual style for the plot
    sns.set_style("darkgrid")

    # Define the desired display order for the x-axis to match the example image
    plot_order = ['Same Week', '2 Week After', '1 Week After', '1 Week Before', '2 Week Before']
    
    # Reindex the data to ensure all categories are present for plotting, even if some are empty
    uplift_by_timing = uplift_by_timing.reindex(plot_order)

    # Define colors based on whether the performance is above or below the overall average
    colors = ['#5A9C6E' if val >= overall_average else '#D44C5C' for val in uplift_by_timing]

    # Create the plot
    plt.figure(figsize=(10, 6))
    barplot = sns.barplot(x=uplift_by_timing.index, y=uplift_by_timing.values, palette=colors)

    # Add the horizontal line representing the overall average
    plt.axhline(y=overall_average, color='#E5A941', linestyle='--', linewidth=2)

    # Add a text label for the overall average line
    plt.text(len(uplift_by_timing) - 1, overall_average, f' Overall Average: {overall_average:.3f}', 
             verticalalignment='center', horizontalalignment='right', color='black', fontsize=11, weight='bold')

    # Add the percentage value on top of each bar, ensuring to handle potential missing data
    for index, value in enumerate(uplift_by_timing):
        if pd.notna(value):
            plt.text(index, value + 0.05, f'{value:.2f}%', 
                     ha='center', va='bottom', fontsize=12, weight='bold')

    # Set titles and labels for clarity
    plt.title('Relative Uplift Performance by Timing\n(Green = Above Average, Red = Below Average)', fontsize=16, weight='bold')
    plt.ylabel('Relative Uplift (%)', fontsize=12)
    plt.xlabel(None)

    # Adjust plot formatting for a clean appearance
    plt.xticks(rotation=45, ha='right')
    # Set a dynamic y-axis limit, with a default if no data is plottable
    y_max = uplift_by_timing.max() if uplift_by_timing.notna().any() else 0
    overall_max = max(y_max, overall_average if pd.notna(overall_average) else 0)
    plt.ylim(0, overall_max * 1.15 if overall_max > 0 else 3.5)
    plt.tight_layout()

    # Display the final plot
    plt.show()

except FileNotFoundError:
    print("File not found. Please ensure the path 'Visuals/KSM_Merged_ads_v3_cleaned.csv' is correct.")
except Exception as e:
    print(f"An error occurred: {e}")


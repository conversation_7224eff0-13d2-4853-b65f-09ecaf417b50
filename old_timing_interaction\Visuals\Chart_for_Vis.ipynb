import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the dataset from the specified path
try:
    df = pd.read_csv('KSM_Merged_ads_v3_cleaned.csv')
    
    # --- Data Processing ---

    # Define the columns that represent the different timing categories
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

    # Unpivot the DataFrame to correctly handle multiple timing categories for a single promotion
    melted_df = df.melt(
        id_vars=['ABI MS Promo Uplift - rel'], 
        value_vars=timing_cols,
        var_name='Timing',
        value_name='Is_Present'
    )

    # Filter for rows where a timing category is actually present (value is 1 or True)
    present_timings = melted_df[melted_df['Is_Present'] == 1].copy()

    # Convert the relative uplift to a percentage for plotting (without multiplying by 100)
    present_timings['Uplift_Percentage'] = present_timings['ABI MS Promo Uplift - rel']

    # Calculate the average uplift for each timing category
    uplift_by_timing = present_timings.groupby('Timing')['Uplift_Percentage'].mean()
    
    # *** CORRECTED LINE ***
    # Rename the index labels for better readability on the plot
    uplift_by_timing.index = uplift_by_timing.index.str.replace(' wk ', ' Week ').str.replace('after', 'After').str.replace('before', 'Before')

    # Calculate the overall average uplift from the complete, original data (without multiplying by 100)
    overall_average = df['ABI MS Promo Uplift - rel'].mean()

    # --- Plotting ---

    # Set the visual style for the plot
    sns.set_style("darkgrid")

    # Define the desired display order for the x-axis to match the example image
    plot_order = ['Same Week', '2 Week After', '1 Week After', '1 Week Before', '2 Week Before']
    
    # Reindex the data to ensure all categories are present for plotting, even if some are empty
    uplift_by_timing = uplift_by_timing.reindex(plot_order)

    # Define colors based on whether the performance is above or below the overall average
    colors = ['#5A9C6E' if val >= overall_average else '#D44C5C' for val in uplift_by_timing]

    # Create the plot
    plt.figure(figsize=(10, 6))
    barplot = sns.barplot(x=uplift_by_timing.index, y=uplift_by_timing.values, palette=colors)

    # Add the horizontal line representing the overall average
    plt.axhline(y=overall_average, color='#E5A941', linestyle='--', linewidth=2)

    # Add a text label for the overall average line
    plt.text(len(uplift_by_timing) - 1, overall_average, f' Overall Average: {overall_average:.3f}', 
             verticalalignment='center', horizontalalignment='right', color='black', fontsize=11, weight='bold')

    # Add the percentage value on top of each bar, ensuring to handle potential missing data
    for index, value in enumerate(uplift_by_timing):
        if pd.notna(value):
            plt.text(index, value + 0.05, f'{value:.2f}', 
                     ha='center', va='bottom', fontsize=12, weight='bold')

    # Set titles and labels for clarity
    plt.title('Relative Uplift Performance by Timing\n(Green = Above Average, Red = Below Average)', fontsize=16, weight='bold')
    plt.ylabel('Relative Uplift', fontsize=12)
    plt.xlabel(None)

    # Adjust plot formatting for a clean appearance
    plt.xticks(rotation=45, ha='right')
    # Set a dynamic y-axis limit, with a default if no data is plottable
    y_max = uplift_by_timing.max() if uplift_by_timing.notna().any() else 0
    overall_max = max(y_max, overall_average if pd.notna(overall_average) else 0)
    plt.ylim(0, overall_max * 1.15 if overall_max > 0 else 3.5)
    plt.tight_layout()

    # Display the final plot
    plt.show()

except FileNotFoundError:
    print("File not found. Please ensure the path 'Visuals/KSM_Merged_ads_v3_cleaned.csv' is correct.")
except Exception as e:
    print(f"An error occurred: {e}")


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the dataset from the specified path
try:
    df = pd.read_csv('KSM_Merged_ads_v3_cleaned.csv')
    
    # --- Data Processing ---

    # Define the columns that represent the different timing categories
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

    # Unpivot the DataFrame to correctly handle multiple timing categories for a single promotion
    melted_df = df.melt(
        id_vars=['ABI MS Promo Uplift - rel'], 
        value_vars=timing_cols,
        var_name='Timing',
        value_name='Is_Present'
    )

    # Filter for rows where a timing category is actually present (value is 1 or True)
    present_timings = melted_df[melted_df['Is_Present'] == 1].copy()

    # Convert the relative uplift to a percentage for plotting (multiply by 100)
    present_timings['Uplift_Percentage'] = present_timings['ABI MS Promo Uplift - rel'] * 100

    # Calculate the average uplift for each timing category
    uplift_by_timing = present_timings.groupby('Timing')['Uplift_Percentage'].mean()
    
    # Rename the index labels for better readability on the plot
    timing_mapping = {
        'Same Week': 'Same Week',
        '2 wk after': '2 Week After', 
        '1 wk after': '1 Week After',
        '1 wk before': '1 Week Before',
        '2 wk before': '2 Week Before'
    }
    uplift_by_timing.index = uplift_by_timing.index.map(timing_mapping)

    # Calculate the overall average uplift from the complete, original data (multiply by 100)
    overall_average = df['ABI MS Promo Uplift - rel'].mean() * 100

    # --- Plotting ---

    # Set the visual style for the plot
    plt.style.use('default')
    
    # Define the desired display order for the x-axis
    plot_order = ['Same Week', '2 Week After', '1 Week After', '1 Week Before', '2 Week Before']
    
    # Reindex the data to ensure all categories are present for plotting
    uplift_by_timing = uplift_by_timing.reindex(plot_order)

    # Define colors based on whether the performance is above or below the overall average
    colors = ['#2E8B57' if val >= overall_average else '#DC143C' for val in uplift_by_timing]

    # Create the plot with specific styling
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create bars
    bars = ax.bar(range(len(uplift_by_timing)), uplift_by_timing.values, 
                  color=colors, alpha=0.8, width=0.6)

    # Add the horizontal line representing the overall average
    ax.axhline(y=overall_average, color='#FFA500', linestyle='--', linewidth=2, alpha=0.8)

    # Add a text label for the overall average line
    ax.text(len(uplift_by_timing) - 0.5, overall_average + 0.5, 
            f'Overall Average: {overall_average:.2f}%', 
            verticalalignment='bottom', horizontalalignment='right', 
            color='#FFA500', fontsize=11, weight='bold')

    # Add the percentage value on top of each bar
    for i, (bar, value) in enumerate(zip(bars, uplift_by_timing)):
        if pd.notna(value):
            ax.text(bar.get_x() + bar.get_width()/2, value + 0.3, 
                   f'{value:.2f}%', 
                   ha='center', va='bottom', fontsize=12, weight='bold')

    # Customize the plot
    ax.set_title('Relative Uplift Performance by Timing\n(Green = Above Average, Red = Below Average)', 
                fontsize=16, weight='bold', pad=20)
    ax.set_ylabel('Relative Uplift (%)', fontsize=12, weight='bold')
    ax.set_xlabel('')

    # Set x-axis labels
    ax.set_xticks(range(len(plot_order)))
    ax.set_xticklabels(plot_order, rotation=0, ha='center')

    # Set y-axis formatting
    ax.set_ylim(0, max(uplift_by_timing.max(), overall_average) * 1.15)
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0f}'))

    # Add grid for better readability
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)

    # Remove top and right spines for cleaner look
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    # Adjust layout
    plt.tight_layout()

    # Display the final plot
    plt.show()

except FileNotFoundError:
    print("File not found. Please ensure the path 'KSM_Merged_ads_v3_cleaned.csv' is correct.")
except Exception as e:
    print(f"An error occurred: {e}")

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the dataset from the specified path
try:
    df = pd.read_csv('new_test_output_ads_v3.csv')
    
    # --- Data Processing ---

    # Define the columns that represent the different timing categories
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

    # Unpivot the DataFrame to correctly handle multiple timing categories for a single promotion
    melted_df = df.melt(
        id_vars=['ABI MS Promo Uplift - rel'], 
        value_vars=timing_cols,
        var_name='Timing',
        value_name='Is_Present'
    )

    # Filter for rows where a timing category is actually present (value is 1 or True)
    present_timings = melted_df[melted_df['Is_Present'] == 1].copy()

    # Convert the relative uplift to a percentage for plotting
    present_timings['Uplift_Percentage'] = present_timings['ABI MS Promo Uplift - rel'] * 100

    # Calculate the average uplift for each timing category
    uplift_by_timing = present_timings.groupby('Timing')['Uplift_Percentage'].mean()
    
    # *** CORRECTED LINE ***
    # Rename the index labels for better readability on the plot
    uplift_by_timing.index = uplift_by_timing.index.str.replace(' wk ', ' Week ').str.replace('after', 'After').str.replace('before', 'Before')

    # Calculate the overall average uplift from the complete, original data
    overall_average = df['ABI MS Promo Uplift - rel'].mean() * 100

    # --- Plotting ---

    # Set the visual style for the plot
    sns.set_style("darkgrid")

    # Define the desired display order for the x-axis to match the example image
    plot_order = ['Same Week', '2 Week After', '1 Week After', '1 Week Before', '2 Week Before']
    
    # Reindex the data to ensure all categories are present for plotting, even if some are empty
    uplift_by_timing = uplift_by_timing.reindex(plot_order)

    # Define colors based on whether the performance is above or below the overall average
    colors = ['#5A9C6E' if val >= overall_average else '#D44C5C' for val in uplift_by_timing]

    # Create the plot
    plt.figure(figsize=(10, 6))
    barplot = sns.barplot(x=uplift_by_timing.index, y=uplift_by_timing.values, palette=colors)

    # Add the horizontal line representing the overall average
    plt.axhline(y=overall_average, color='#E5A941', linestyle='--', linewidth=2)

    # Add a text label for the overall average line
    plt.text(len(uplift_by_timing) - 1, overall_average, f' Overall Average: {overall_average:.3f}', 
             verticalalignment='center', horizontalalignment='right', color='black', fontsize=11, weight='bold')

    # Add the percentage value on top of each bar, ensuring to handle potential missing data
    for index, value in enumerate(uplift_by_timing):
        if pd.notna(value):
            plt.text(index, value + 0.05, f'{value:.2f}%', 
                     ha='center', va='bottom', fontsize=12, weight='bold')

    # Set titles and labels for clarity
    plt.title('Relative Uplift Performance by Timing\n(Green = Above Average, Red = Below Average)', fontsize=16, weight='bold')
    plt.ylabel('Relative Uplift (%)', fontsize=12)
    plt.xlabel(None)

    # Adjust plot formatting for a clean appearance
    plt.xticks(rotation=45, ha='right')
    # Set a dynamic y-axis limit, with a default if no data is plottable
    y_max = uplift_by_timing.max() if uplift_by_timing.notna().any() else 0
    overall_max = max(y_max, overall_average if pd.notna(overall_average) else 0)
    plt.ylim(0, overall_max * 1.15 if overall_max > 0 else 3.5)
    plt.tight_layout()

    # Display the final plot
    plt.show()

except FileNotFoundError:
    print("File not found. Please ensure the path 'Visuals/KSM_Merged_ads_v3_cleaned.csv' is correct.")
except Exception as e:
    print(f"An error occurred: {e}")


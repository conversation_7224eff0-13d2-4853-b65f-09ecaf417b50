#!/usr/bin/env python3
"""
Debug the aggregation logic by examining the actual data
"""

import pandas as pd
import numpy as np

def debug_existing_csvs():
    """Debug the existing CSV files to understand the data structure"""
    
    print("=== DEBUGGING EXISTING CSV FILES ===")
    
    try:
        # Load the melted CSV (should be the raw data)
        melted_df = pd.read_csv('ads_melted_v3.csv')
        print(f"Loaded melted CSV: {len(melted_df)} rows")
        
        # Focus on interaction rows only
        interaction_df = melted_df[
            melted_df['Competitor SKU'].notna() & 
            (melted_df['Competitor SKU'] != '')
        ].copy()
        
        print(f"Interaction rows: {len(interaction_df)}")
        print(f"Non-interaction rows: {len(melted_df) - len(interaction_df)}")
        
        # Check for potential duplicates that should be merged
        print(f"\n=== LOOKING FOR MERGE CANDIDATES ===")
        
        # Group by the same columns we use in the aggregation
        group_cols = ['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU',
                      'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic',
                      'ABI Depth', 'ABI Rounded']
        
        # Check if all required columns exist
        missing_cols = [col for col in group_cols if col not in interaction_df.columns]
        if missing_cols:
            print(f"Missing columns: {missing_cols}")
            # Use available columns
            available_cols = [col for col in group_cols if col in interaction_df.columns]
            group_cols = available_cols
            print(f"Using available columns: {group_cols}")
        
        # Group and find duplicates
        grouped = interaction_df.groupby(group_cols).size()
        duplicates = grouped[grouped > 1]
        
        print(f"Cases where same comp SKU has multiple promos vs same ABI promo: {len(duplicates)}")
        
        if len(duplicates) > 0:
            print(f"\nFound {len(duplicates)} cases that should be merged!")
            print("Sample cases:")
            
            for i, (group_key, count) in enumerate(duplicates.head(5).items()):
                print(f"\nCase {i+1}: {count} rows to merge")
                if isinstance(group_key, tuple) and len(group_key) >= 4:
                    print(f"  Retailer: {group_key[0]}")
                    print(f"  ABI PromoID: {group_key[1]}")
                    print(f"  ABI SKU: {group_key[2]}")
                    print(f"  Competitor SKU: {group_key[3]}")
                
                # Show the actual rows
                if isinstance(group_key, tuple):
                    mask = True
                    for j, col in enumerate(group_cols):
                        if j < len(group_key):
                            mask = mask & (interaction_df[col] == group_key[j])
                else:
                    mask = interaction_df[group_cols[0]] == group_key
                
                sample_rows = interaction_df[mask]
                if 'Competitor PromoID' in sample_rows.columns:
                    promo_ids = sample_rows['Competitor PromoID'].tolist()
                    print(f"  Competitor PromoIDs: {promo_ids}")
                
                if 'Competitor Start' in sample_rows.columns and 'Competitor End' in sample_rows.columns:
                    dates = list(zip(sample_rows['Competitor Start'], sample_rows['Competitor End']))
                    print(f"  Date ranges: {dates}")
        else:
            print("No cases found where same comp SKU has multiple promos vs same ABI promo")
            print("This explains why both modes produce the same row count")
            
            # Let's look at the data structure more closely
            print(f"\n=== DATA STRUCTURE ANALYSIS ===")
            print(f"Unique ABI PromoIDs: {interaction_df['ABI PromoID'].nunique()}")
            print(f"Unique Competitor SKUs: {interaction_df['Competitor SKU'].nunique()}")
            
            # Check for any patterns
            print(f"\nSample of interaction data:")
            sample_cols = ['ABI PromoID', 'Competitor SKU', 'Competitor PromoID']
            available_sample_cols = [col for col in sample_cols if col in interaction_df.columns]
            if available_sample_cols:
                print(interaction_df[available_sample_cols].head(10).to_string())
            
            # Check if there are multiple competitor promos per ABI promo (different comp SKUs)
            abi_comp_counts = interaction_df.groupby('ABI PromoID')['Competitor SKU'].nunique()
            multi_comp_abis = abi_comp_counts[abi_comp_counts > 1]
            print(f"\nABI promos with multiple competitor SKUs: {len(multi_comp_abis)}")
            if len(multi_comp_abis) > 0:
                print("Sample ABI promos with multiple competitors:")
                for abi_promo, comp_count in multi_comp_abis.head(3).items():
                    print(f"  {abi_promo}: {comp_count} competitor SKUs")
                    comp_skus = interaction_df[interaction_df['ABI PromoID'] == abi_promo]['Competitor SKU'].unique()
                    print(f"    Competitor SKUs: {list(comp_skus)}")
        
        # Test the aggregation logic manually
        print(f"\n=== MANUAL AGGREGATION TEST ===")
        
        if len(duplicates) > 0:
            print("Applying aggregation logic...")
            
            agg_dict = {
                'Competitor PromoID': lambda x: '|'.join(sorted(set(x.dropna().astype(str)))),
                'Competitor Start': 'min',
                'Competitor End': 'max',
                'Competitor Coverage': 'mean',
                'Competitor Depth': 'first',
            }
            
            # Only include columns that exist
            available_agg_dict = {}
            for col, func in agg_dict.items():
                if col in interaction_df.columns:
                    available_agg_dict[col] = func
            
            if available_agg_dict:
                aggregated = interaction_df.groupby(group_cols, as_index=False).agg(available_agg_dict)
                print(f"After aggregation: {len(aggregated)} rows")
                print(f"Rows reduced by: {len(interaction_df) - len(aggregated)}")
                
                # Show sample of aggregated data
                pipe_joined = aggregated['Competitor PromoID'].str.contains('\\|', na=False).sum() if 'Competitor PromoID' in aggregated.columns else 0
                print(f"Rows with pipe-joined Competitor PromoIDs: {pipe_joined}")
            else:
                print("No aggregation columns available")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_existing_csvs()

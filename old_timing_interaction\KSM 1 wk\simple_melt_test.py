#!/usr/bin/env python3
"""
Simple test to verify the MELT_SKU_FLAG logic works correctly.
This creates synthetic data to test the melting behavior.
"""

import pandas as pd
import numpy as np

def test_melt_logic(melt_flag_value):
    """Test the melting logic with the given flag value"""
    
    print(f"\n{'='*60}")
    print(f"TESTING WITH MELT_SKU_FLAG = {melt_flag_value}")
    print(f"{'='*60}")
    
    # Set the flag
    MELT_SKU_FLAG = melt_flag_value
    
    # Create test interaction records
    print("Creating test interaction data...")
    
    test_records = [
        {
            'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
            'Competitor SKU': 'COMP_SKU_1', 'Competitor PromoID': 'COMP_001',
            'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
            'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
            'Competitor Start': pd.Timestamp('2022-05-02'), 'Competitor End': pd.Timestamp('2022-05-08'),
            'Competitor Coverage': 80, 'Competitor Depth': 15, 'Timing': 'Overlap',
            'Weeks since last comp promo': 2, 'Weeks until next comp promo': 3,
            'Overlap Promos': 1, 'Actual Overlap Days': 6, 'Category': 'Interaction'
        },
        {
            'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
            'Competitor SKU': 'COMP_SKU_1', 'Competitor PromoID': 'COMP_002',  # Same comp SKU, different promo
            'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
            'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
            'Competitor Start': pd.Timestamp('2022-05-03'), 'Competitor End': pd.Timestamp('2022-05-09'),
            'Competitor Coverage': 75, 'Competitor Depth': 18, 'Timing': 'Overlap',
            'Weeks since last comp promo': 1, 'Weeks until next comp promo': 4,
            'Overlap Promos': 1, 'Actual Overlap Days': 5, 'Category': 'Interaction'
        },
        {
            'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
            'Competitor SKU': 'COMP_SKU_2', 'Competitor PromoID': 'COMP_003',  # Different comp SKU
            'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
            'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
            'Competitor Start': pd.Timestamp('2022-05-04'), 'Competitor End': pd.Timestamp('2022-05-10'),
            'Competitor Coverage': 90, 'Competitor Depth': 25, 'Timing': 'Overlap',
            'Weeks since last comp promo': 0, 'Weeks until next comp promo': 2,
            'Overlap Promos': 1, 'Actual Overlap Days': 4, 'Category': 'Interaction'
        },
        {
            'Retailer': 'RETAILER_A', 'ABI PromoID': 'ABI_001', 'ABI SKU': 'ABI_SKU_X',
            'Competitor SKU': 'COMP_SKU_1', 'Competitor PromoID': 'COMP_004',  # Same comp SKU again, third promo
            'ABI Start': pd.Timestamp('2022-05-01'), 'ABI End': pd.Timestamp('2022-05-07'),
            'ABI Coverage': 100, 'ABI Mechanic': 'LV', 'ABI Depth': 20, 'ABI Rounded': 20,
            'Competitor Start': pd.Timestamp('2022-05-05'), 'Competitor End': pd.Timestamp('2022-05-11'),
            'Competitor Coverage': 85, 'Competitor Depth': 12, 'Timing': 'Overlap',
            'Weeks since last comp promo': 0, 'Weeks until next comp promo': 5,
            'Overlap Promos': 1, 'Actual Overlap Days': 3, 'Category': 'Interaction'
        }
    ]
    
    # Add some non-interaction records
    no_interaction_records = [
        {
            'Retailer': 'RETAILER_B', 'ABI PromoID': 'ABI_002', 'ABI SKU': 'ABI_SKU_Y',
            'Competitor SKU': '', 'Competitor PromoID': 'No interaction',
            'ABI Start': pd.Timestamp('2022-06-01'), 'ABI End': pd.Timestamp('2022-06-07'),
            'ABI Coverage': 100, 'ABI Mechanic': 'FID', 'ABI Depth': 15, 'ABI Rounded': 15,
            'Competitor Start': pd.NaT, 'Competitor End': pd.NaT,
            'Competitor Coverage': np.nan, 'Competitor Depth': np.nan, 'Timing': np.nan,
            'Weeks since last comp promo': 999, 'Weeks until next comp promo': -999,
            'Overlap Promos': 0, 'Actual Overlap Days': 0, 'Category': 'No interaction'
        }
    ]
    
    # Create the final dataframe
    final_df = pd.DataFrame(test_records + no_interaction_records)
    
    print(f"Test data created: {len(final_df)} rows")
    print(f"- Interaction records: {len(test_records)}")
    print(f"- No-interaction records: {len(no_interaction_records)}")
    
    print(f"\nBefore processing:")
    print("Interaction records:")
    interaction_df = final_df[final_df['Category'] == 'Interaction']
    for _, row in interaction_df.iterrows():
        print(f"  ABI: {row['ABI PromoID']} | Comp SKU: {row['Competitor SKU']} | Comp Promo: {row['Competitor PromoID']}")
    
    # Apply the melting logic
    print(f"\nApplying MELT_SKU_FLAG = {MELT_SKU_FLAG} logic...")
    
    if not MELT_SKU_FLAG:
        print(f'Before aggregation: {len(final_df)} rows')
        interaction_mask = (final_df['Competitor SKU'].notna()) & (final_df['Competitor SKU'] != '')
        int_df = final_df[interaction_mask]
        non_int_df = final_df[~interaction_mask]
        print(f'Interaction rows to aggregate: {len(int_df)}')
        print(f'Non-interaction rows (unchanged): {len(non_int_df)}')

        # Group by ABI promo details INCLUDING Competitor SKU
        # This preserves different competitor SKUs as separate rows
        # Only merges when same competitor SKU has multiple promos
        group_cols = ['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU',
                      'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic',
                      'ABI Depth', 'ABI Rounded']

        agg_dict = {
            'Competitor PromoID': lambda x: '|'.join(sorted(set(x.dropna().astype(str)))),  # Pipe-join multiple promo IDs
            'Competitor Start': 'min',  # Earliest start date
            'Competitor End': 'max',    # Latest end date
            'Competitor Coverage': 'mean',  # Average coverage
            'Competitor Depth': 'first',    # Take first depth value
            'Timing': 'first',              # Take first timing category
            'Weeks since last comp promo': 'first',
            'Weeks until next comp promo': 'first',
            'Overlap Promos': 'max',        # Maximum overlap count
            'Actual Overlap Days': 'max',   # Maximum overlap days
            'Category': 'first'             # Take first category
        }
        
        int_df_aggregated = int_df.groupby(group_cols, as_index=False).agg(agg_dict)
        print(f'After aggregation: {len(int_df_aggregated)} interaction rows')
        print(f'Rows reduced by: {len(int_df) - len(int_df_aggregated)} (same comp SKU with multiple promos)')
        final_df = pd.concat([int_df_aggregated, non_int_df], ignore_index=True)
        print(f'Final total rows: {len(final_df)}')
    else:
        print(f'Melted mode: {len(final_df)} rows (no aggregation)')
    
    # Show results
    print(f"\n=== RESULTS FOR MELT_SKU_FLAG = {MELT_SKU_FLAG} ===")
    print(f"Total rows: {len(final_df)}")
    print(f"Unique ABI PromoIDs: {final_df['ABI PromoID'].nunique()}")
    comp_skus = final_df[final_df['Competitor SKU'].notna() & (final_df['Competitor SKU'] != '')]['Competitor SKU']
    print(f"Unique Competitor SKUs: {comp_skus.nunique()}")
    
    # Show the actual data
    print(f"\nFinal DataFrame (interaction rows only):")
    interaction_final = final_df[final_df['Category'] == 'Interaction']
    for _, row in interaction_final.iterrows():
        print(f"  ABI: {row['ABI PromoID']} | Comp SKU: {row['Competitor SKU']} | Comp Promo: {row['Competitor PromoID']}")
    
    if not MELT_SKU_FLAG:
        # Check for pipe-joined promo IDs
        pipe_joined_promos = final_df['Competitor PromoID'].str.contains('\\|', na=False).sum()
        print(f"\nRows with pipe-joined Competitor PromoIDs: {pipe_joined_promos}")
        if pipe_joined_promos > 0:
            merged_rows = final_df[final_df['Competitor PromoID'].str.contains('\\|', na=False)]
            print("Merged rows details:")
            for _, row in merged_rows.iterrows():
                print(f"  Comp SKU: {row['Competitor SKU']} | Promo IDs: {row['Competitor PromoID']}")
                print(f"    Date range: {row['Competitor Start'].date()} to {row['Competitor End'].date()}")
    
    return final_df

if __name__ == "__main__":
    print("Testing MELT_SKU_FLAG logic with synthetic data...")
    
    # Test both modes
    result_melted = test_melt_logic(True)
    result_unmelted = test_melt_logic(False)
    
    print(f"\n{'='*60}")
    print("COMPARISON SUMMARY")
    print(f"{'='*60}")
    print(f"MELTED mode rows:   {len(result_melted)}")
    print(f"UNMELTED mode rows: {len(result_unmelted)}")
    print(f"Difference:         {len(result_melted) - len(result_unmelted)}")
    
    if len(result_melted) > len(result_unmelted):
        print("✅ SUCCESS: UNMELTED mode has fewer rows (as expected)")
        print("   This means same competitor SKUs with multiple promos were merged")
    elif len(result_melted) == len(result_unmelted):
        print("⚠️  WARNING: Both modes have same row count")
        print("   This could be normal if no competitor SKU has multiple promos")
    else:
        print("❌ ERROR: MELTED mode has fewer rows (unexpected)")
    
    print(f"\nKey insight:")
    print(f"- Different competitor SKUs remain as separate rows in both modes")
    print(f"- Only same competitor SKU with multiple promos get merged in UNMELTED mode")
    print(f"- Competitor SKU information is preserved in both modes")

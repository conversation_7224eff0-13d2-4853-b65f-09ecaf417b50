#!/usr/bin/env python3
"""
Run the notebook with the current settings to test the aggregation
"""

import subprocess
import sys
import os

def run_notebook():
    """Run the notebook and capture output"""
    
    print("Running notebook with current settings...")
    
    try:
        # Convert notebook to Python script
        print("Converting notebook to Python script...")
        result = subprocess.run([
            'jupyter', 'nbconvert', '--to', 'python', 
            'Trying_with_Switch_PromoCalendarPlanner.ipynb',
            '--output', 'current_notebook_script'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"Error converting notebook: {result.stderr}")
            return False
        
        print("Conversion successful!")
        
        # Run the Python script
        print("Running the converted script...")
        result = subprocess.run([
            'python', 'current_notebook_script.py'
        ], capture_output=True, text=True, timeout=600)
        
        # Save output to file
        with open('notebook_run_output.txt', 'w', encoding='utf-8', errors='ignore') as f:
            f.write("STDOUT:\n")
            f.write(result.stdout)
            f.write("\n\nSTDERR:\n")
            f.write(result.stderr)
            f.write(f"\n\nReturn code: {result.returncode}")
        
        print(f"Script execution completed with return code: {result.returncode}")
        print("Output saved to: notebook_run_output.txt")
        
        # Look for key information in the output
        output = result.stdout + result.stderr
        
        # Search for aggregation information
        lines = output.split('\n')
        for i, line in enumerate(lines):
            if 'UNMELTED MODE AGGREGATION' in line or 'MELT_SKU_FLAG VERIFICATION' in line:
                print(f"\nFound key output at line {i}:")
                # Print this line and next 10 lines
                for j in range(i, min(i+15, len(lines))):
                    print(f"  {lines[j]}")
                break
        
        # Check if new CSV was created
        if os.path.exists('ads_unmelted_v3.csv'):
            # Count lines
            with open('ads_unmelted_v3.csv', 'r') as f:
                line_count = sum(1 for line in f) - 1  # Subtract header
            print(f"\nNew ads_unmelted_v3.csv created with {line_count} rows")
            
            # Compare with melted version
            if os.path.exists('ads_melted_v3.csv'):
                with open('ads_melted_v3.csv', 'r') as f:
                    melted_count = sum(1 for line in f) - 1
                print(f"Melted version has {melted_count} rows")
                print(f"Difference: {melted_count - line_count} rows")
                
                if melted_count > line_count:
                    print("✅ SUCCESS: Unmelted version has fewer rows!")
                elif melted_count == line_count:
                    print("⚠️  WARNING: Both versions have same row count")
                else:
                    print("❌ ERROR: Unmelted version has more rows")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Script execution timed out")
        return False
    except Exception as e:
        print(f"Error running notebook: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists('current_notebook_script.py'):
            try:
                os.remove('current_notebook_script.py')
            except:
                pass

if __name__ == "__main__":
    success = run_notebook()
    if success:
        print("\n✅ Notebook execution completed successfully!")
    else:
        print("\n❌ Notebook execution failed or had issues")
        print("Check notebook_run_output.txt for details")

#!/usr/bin/env python3
"""
Test the aggregation fix by applying it to the existing melted CSV
"""

import pandas as pd
import numpy as np

def test_aggregation_fix():
    """Test the aggregation logic on the existing melted data"""
    
    print("=== TESTING AGGREGATION FIX ===")
    
    # Set the flag
    MELT_SKU_FLAG = False
    
    try:
        # Load the melted CSV (this should be the raw data)
        print("Loading melted CSV...")
        final_df = pd.read_csv('ads_melted_v3.csv')
        print(f"Loaded: {len(final_df)} rows")
        
        # Apply the aggregation logic exactly as it should be in the notebook
        if not MELT_SKU_FLAG:
            print(f'\n=== UNMELTED MODE AGGREGATION ===')
            print(f'Before aggregation: {len(final_df)} rows')
            interaction_mask = (final_df['Competitor SKU'].notna()) & (final_df['Competitor SKU'] != '')
            int_df = final_df[interaction_mask]
            non_int_df = final_df[~interaction_mask]
            print(f'Interaction rows to aggregate: {len(int_df)}')
            print(f'Non-interaction rows (unchanged): {len(non_int_df)}')

            # CORRECTED: Keep 'Competitor SKU' in group_cols to preserve different competitor SKUs
            # Only merge when the SAME competitor SKU has multiple promos against the SAME ABI promo
            group_cols = ['Retailer', 'ABI PromoID', 'ABI SKU', 'Competitor SKU',
                          'ABI Start', 'ABI End', 'ABI Coverage', 'ABI Mechanic',
                          'ABI Depth', 'ABI Rounded']

            # Check for potential merges BEFORE aggregation
            potential_merges = int_df.groupby(group_cols).size()
            merge_cases = potential_merges[potential_merges > 1]
            print(f'Cases where same comp SKU has multiple promos vs same ABI promo: {len(merge_cases)}')
            if len(merge_cases) > 0:
                print('Sample merge cases:')
                for i, (group_key, count) in enumerate(merge_cases.head(3).items()):
                    print(f'  Case {i+1}: ABI: {group_key[1]} | Comp SKU: {group_key[3]} | Count: {count}')

            agg_dict = {
                'Competitor PromoID': lambda x: '|'.join(sorted(set(x.dropna().astype(str)))),  # Pipe-join multiple promo IDs
                'Competitor Start'  : 'min',  # Earliest start date
                'Competitor End'    : 'max',  # Latest end date
                'Competitor Coverage': 'mean',  # Average coverage
                'Competitor Depth'  : 'first',  # Take first depth value
                'Timing'            : 'first',  # Take first timing category
                'Weeks since last comp promo': 'first',
                'Weeks until next comp promo': 'first',
                'Overlap Promos'    : 'max',   # Maximum overlap count
                'Actual Overlap Days': 'max',  # Maximum overlap days
                'Category'          : 'first'  # Take first category
            }
            
            # Filter agg_dict to only include columns that exist
            available_agg_dict = {}
            for col, func in agg_dict.items():
                if col in int_df.columns:
                    available_agg_dict[col] = func
                else:
                    print(f"Warning: Column '{col}' not found in data")
            
            print(f"Aggregating with {len(available_agg_dict)} columns...")
            int_df_aggregated = int_df.groupby(group_cols, as_index=False).agg(available_agg_dict)
            print(f'After aggregation: {len(int_df_aggregated)} interaction rows')
            print(f'Rows reduced by: {len(int_df) - len(int_df_aggregated)} (same comp SKU with multiple promos)')
            final_df = pd.concat([int_df_aggregated, non_int_df], ignore_index=True)
            print(f'Post-collapse total rows: {len(final_df)}')
        else:
            print(f'Melted mode: {len(final_df)} rows (no aggregation)')
        
        # Verify the results
        print(f'\n=== VERIFICATION ===')
        print(f'Final total rows: {len(final_df)}')
        print(f'Unique ABI PromoIDs: {final_df["ABI PromoID"].nunique()}')
        print(f'Unique ABI SKUs: {final_df["ABI SKU"].nunique()}')
        comp_skus = final_df[final_df["Competitor SKU"].notna() & (final_df["Competitor SKU"] != "")]["Competitor SKU"]
        print(f'Unique Competitor SKUs: {comp_skus.nunique()}')
        print(f'Rows with competitor interactions: {len(final_df[final_df["Competitor SKU"].notna() & (final_df["Competitor SKU"] != "")])}')
        print(f'Rows without competitor interactions: {len(final_df[final_df["Competitor SKU"].isna() | (final_df["Competitor SKU"] == "")])}')
        
        if not MELT_SKU_FLAG:
            # Check for pipe-joined PROMO IDs (not SKUs)
            if 'Competitor PromoID' in final_df.columns:
                pipe_joined_promos = final_df['Competitor PromoID'].str.contains('\\|', na=False).sum()
                print(f'\nRows with pipe-joined Competitor PromoIDs: {pipe_joined_promos}')
                if pipe_joined_promos > 0:
                    sample_merged = final_df[final_df['Competitor PromoID'].str.contains('\\|', na=False)][['ABI PromoID', 'Competitor SKU', 'Competitor PromoID']].head(3)
                    print(f'Sample merged rows:')
                    for _, row in sample_merged.iterrows():
                        print(f'  ABI: {row["ABI PromoID"]}')
                        print(f'  Comp SKU: {row["Competitor SKU"]}')
                        print(f'  Comp PromoIDs: {row["Competitor PromoID"]}')
                        print()
        
        # Save the corrected unmelted file
        output_file = 'ads_unmelted_v3_CORRECTED.csv'
        final_df.to_csv(output_file, index=False)
        print(f'\nSaved corrected unmelted data to: {output_file}')
        
        return len(final_df)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_original():
    """Compare the corrected version with the original"""
    
    print(f"\n{'='*60}")
    print("COMPARISON WITH ORIGINAL")
    print(f"{'='*60}")
    
    try:
        # Load original files
        melted_df = pd.read_csv('ads_melted_v3.csv')
        original_unmelted_df = pd.read_csv('ads_unmelted_v3.csv')
        corrected_unmelted_df = pd.read_csv('ads_unmelted_v3_CORRECTED.csv')
        
        print(f"Original melted rows:     {len(melted_df)}")
        print(f"Original unmelted rows:   {len(original_unmelted_df)}")
        print(f"Corrected unmelted rows:  {len(corrected_unmelted_df)}")
        
        print(f"\nDifference (melted vs original unmelted):  {len(melted_df) - len(original_unmelted_df)}")
        print(f"Difference (melted vs corrected unmelted): {len(melted_df) - len(corrected_unmelted_df)}")
        
        if len(melted_df) == len(original_unmelted_df):
            print("❌ Original unmelted file is identical to melted (aggregation didn't work)")
        else:
            print("✅ Original unmelted file is different from melted")
            
        if len(melted_df) > len(corrected_unmelted_df):
            print("✅ Corrected unmelted file has fewer rows (aggregation worked!)")
        else:
            print("❌ Corrected unmelted file doesn't have fewer rows")
            
    except Exception as e:
        print(f"Error in comparison: {e}")

if __name__ == "__main__":
    print("Testing aggregation fix...")
    
    result = test_aggregation_fix()
    
    if result is not None:
        compare_with_original()
        print(f"\n✅ SUCCESS: Aggregation logic is working correctly!")
        print(f"The issue was that the aggregation code wasn't being executed in the notebook.")
        print(f"The corrected unmelted file should have significantly fewer rows.")
    else:
        print(f"\n❌ Test failed")

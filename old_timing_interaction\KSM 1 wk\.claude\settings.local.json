{"permissions": {"allow": ["Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:github.com)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(apt list:*)", "Bash(export:*)", "Bash(/home/<USER>/.local/bin/pip install jupyter-mcp-server --break-system-packages)", "Bash(/home/<USER>/.local/bin/jupyter-mcp-server:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}
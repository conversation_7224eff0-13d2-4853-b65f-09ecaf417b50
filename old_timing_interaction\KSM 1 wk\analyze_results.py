#!/usr/bin/env python3
"""
Analyze the results from both melted and unmelted CSV files
"""

import pandas as pd

def analyze_csv_files():
    print("Analyzing CSV files...")
    
    try:
        # Load both CSV files
        melted_df = pd.read_csv('ads_melted_v3.csv')
        unmelted_df = pd.read_csv('ads_unmelted_v3.csv')
        
        print(f"\n{'='*60}")
        print("FILE COMPARISON")
        print(f"{'='*60}")
        print(f"Melted CSV rows:   {len(melted_df)}")
        print(f"Unmelted CSV rows: {len(unmelted_df)}")
        print(f"Difference:        {len(melted_df) - len(unmelted_df)}")
        
        if len(melted_df) == len(unmelted_df):
            print("⚠️  Both files have the same number of rows!")
            print("This suggests the melting logic may not be working as expected.")
        
        # Check for differences in the data
        print(f"\n{'='*60}")
        print("DATA ANALYSIS")
        print(f"{'='*60}")
        
        # Analyze competitor interactions
        melted_interactions = melted_df[melted_df['Competitor SKU'].notna() & (melted_df['Competitor SKU'] != '')]
        unmelted_interactions = unmelted_df[unmelted_df['Competitor SKU'].notna() & (unmelted_df['Competitor SKU'] != '')]
        
        print(f"Melted - Interaction rows:   {len(melted_interactions)}")
        print(f"Unmelted - Interaction rows: {len(unmelted_interactions)}")
        
        # Check for pipe-joined competitor promo IDs in unmelted
        if 'Competitor PromoID' in unmelted_df.columns:
            pipe_joined = unmelted_df['Competitor PromoID'].str.contains('\\|', na=False).sum()
            print(f"Unmelted - Rows with pipe-joined Competitor PromoIDs: {pipe_joined}")
            
            if pipe_joined > 0:
                print("\nSample pipe-joined rows:")
                sample_merged = unmelted_df[unmelted_df['Competitor PromoID'].str.contains('\\|', na=False)].head(3)
                for _, row in sample_merged.iterrows():
                    print(f"  ABI: {row['ABI PromoID']}")
                    print(f"  Comp SKU: {row['Competitor SKU']}")
                    print(f"  Comp PromoIDs: {row['Competitor PromoID']}")
                    print()
        
        # Check unique counts
        print(f"\nUnique ABI PromoIDs:")
        print(f"  Melted:   {melted_df['ABI PromoID'].nunique()}")
        print(f"  Unmelted: {unmelted_df['ABI PromoID'].nunique()}")
        
        print(f"\nUnique Competitor SKUs (non-empty):")
        melted_comp_skus = melted_df[melted_df['Competitor SKU'].notna() & (melted_df['Competitor SKU'] != '')]['Competitor SKU'].nunique()
        unmelted_comp_skus = unmelted_df[unmelted_df['Competitor SKU'].notna() & (unmelted_df['Competitor SKU'] != '')]['Competitor SKU'].nunique()
        print(f"  Melted:   {melted_comp_skus}")
        print(f"  Unmelted: {unmelted_comp_skus}")
        
        # Look for potential cases where same competitor SKU has multiple promos
        print(f"\n{'='*60}")
        print("POTENTIAL MERGE CASES ANALYSIS")
        print(f"{'='*60}")
        
        # Group by ABI PromoID and Competitor SKU to see if there are duplicates
        melted_grouped = melted_interactions.groupby(['ABI PromoID', 'Competitor SKU']).size()
        multiple_promos = melted_grouped[melted_grouped > 1]
        
        print(f"Cases where same Competitor SKU has multiple interactions with same ABI Promo:")
        print(f"Count: {len(multiple_promos)}")
        
        if len(multiple_promos) > 0:
            print("\nSample cases:")
            for (abi_promo, comp_sku), count in multiple_promos.head(5).items():
                print(f"  ABI Promo: {abi_promo}")
                print(f"  Competitor SKU: {comp_sku}")
                print(f"  Number of interactions: {count}")
                
                # Show the actual records
                sample_records = melted_interactions[
                    (melted_interactions['ABI PromoID'] == abi_promo) & 
                    (melted_interactions['Competitor SKU'] == comp_sku)
                ]
                if 'Competitor PromoID' in sample_records.columns:
                    promo_ids = sample_records['Competitor PromoID'].tolist()
                    print(f"  Competitor PromoIDs: {promo_ids}")
                print()
        else:
            print("No cases found where same Competitor SKU has multiple interactions with same ABI Promo.")
            print("This explains why both files have the same row count.")
        
        # Check if files are identical
        print(f"\n{'='*60}")
        print("FILE IDENTITY CHECK")
        print(f"{'='*60}")
        
        # Compare a few key columns
        if len(melted_df) == len(unmelted_df):
            # Sort both dataframes for comparison
            melted_sorted = melted_df.sort_values(['ABI PromoID', 'Competitor SKU']).reset_index(drop=True)
            unmelted_sorted = unmelted_df.sort_values(['ABI PromoID', 'Competitor SKU']).reset_index(drop=True)
            
            # Compare key columns
            key_cols = ['ABI PromoID', 'Competitor SKU', 'Competitor PromoID']
            available_cols = [col for col in key_cols if col in melted_df.columns and col in unmelted_df.columns]
            
            if available_cols:
                are_identical = melted_sorted[available_cols].equals(unmelted_sorted[available_cols])
                print(f"Key columns identical: {are_identical}")
                
                if not are_identical:
                    print("Files have differences in key columns.")
                else:
                    print("Files appear to be identical in key columns.")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_csv_files()

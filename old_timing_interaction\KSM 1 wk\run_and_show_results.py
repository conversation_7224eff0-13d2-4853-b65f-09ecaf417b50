#!/usr/bin/env python3
"""
Run the notebook and show the results clearly
"""

import subprocess
import sys
import os
import pandas as pd

def run_notebook_and_show_results():
    """Run the notebook and show clear results"""
    
    print("="*60)
    print("RUNNING NOTEBOOK WITH MELT_SKU_FLAG = False")
    print("="*60)
    
    try:
        # Convert notebook to Python script
        print("1. Converting notebook to Python script...")
        result = subprocess.run([
            'jupyter', 'nbconvert', '--to', 'python', 
            'Trying_with_Switch_PromoCalendarPlanner.ipynb',
            '--output', 'test_script'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"❌ Error converting notebook: {result.stderr}")
            return False
        
        print("✅ Conversion successful!")
        
        # Run the Python script
        print("\n2. Running the converted script...")
        result = subprocess.run([
            'python', 'test_script.py'
        ], capture_output=True, text=True, timeout=600)
        
        print(f"✅ Script execution completed with return code: {result.returncode}")
        
        # Save full output
        with open('full_execution_output.txt', 'w', encoding='utf-8', errors='ignore') as f:
            f.write("STDOUT:\n")
            f.write(result.stdout)
            f.write("\n\nSTDERR:\n")
            f.write(result.stderr)
            f.write(f"\n\nReturn code: {result.returncode}")
        
        # Extract key information from output
        output = result.stdout + result.stderr
        lines = output.split('\n')
        
        print("\n" + "="*60)
        print("KEY EXECUTION RESULTS")
        print("="*60)
        
        # Look for aggregation information
        found_aggregation = False
        for i, line in enumerate(lines):
            if 'UNMELTED MODE AGGREGATION' in line:
                found_aggregation = True
                print("✅ Found aggregation section:")
                # Print this line and next 15 lines
                for j in range(i, min(i+20, len(lines))):
                    if lines[j].strip():  # Only print non-empty lines
                        print(f"   {lines[j]}")
                break
        
        if not found_aggregation:
            print("❌ Aggregation section not found in output")
            # Look for any mention of final_df
            for i, line in enumerate(lines):
                if 'final_df' in line and ('rows' in line or 'len' in line):
                    print(f"Found: {line}")
        
        # Look for verification information
        found_verification = False
        for i, line in enumerate(lines):
            if 'MELT_SKU_FLAG VERIFICATION' in line:
                found_verification = True
                print("\n✅ Found verification section:")
                # Print this line and next 10 lines
                for j in range(i, min(i+15, len(lines))):
                    if lines[j].strip():  # Only print non-empty lines
                        print(f"   {lines[j]}")
                break
        
        if not found_verification:
            print("❌ Verification section not found in output")
        
        # Check CSV files
        print("\n" + "="*60)
        print("CSV FILE ANALYSIS")
        print("="*60)
        
        csv_files = ['ads_melted_v3.csv', 'ads_unmelted_v3.csv']
        file_info = {}
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                try:
                    df = pd.read_csv(csv_file)
                    file_info[csv_file] = len(df)
                    print(f"✅ {csv_file}: {len(df)} rows")
                except Exception as e:
                    print(f"❌ Error reading {csv_file}: {e}")
            else:
                print(f"❌ {csv_file}: File not found")
        
        # Compare results
        if 'ads_melted_v3.csv' in file_info and 'ads_unmelted_v3.csv' in file_info:
            melted_rows = file_info['ads_melted_v3.csv']
            unmelted_rows = file_info['ads_unmelted_v3.csv']
            difference = melted_rows - unmelted_rows
            
            print(f"\n📊 COMPARISON:")
            print(f"   Melted rows:   {melted_rows}")
            print(f"   Unmelted rows: {unmelted_rows}")
            print(f"   Difference:    {difference}")
            
            if difference > 0:
                print(f"✅ SUCCESS! Unmelted mode reduced rows by {difference}")
                print("   This means the aggregation logic is working correctly!")
            elif difference == 0:
                print("⚠️  WARNING: Both files have the same row count")
                print("   The aggregation logic may not have executed properly")
            else:
                print("❌ ERROR: Unmelted has more rows than melted")
        
        # Look for any errors
        if result.returncode != 0:
            print(f"\n❌ EXECUTION ERRORS:")
            error_lines = [line for line in lines if 'error' in line.lower() or 'traceback' in line.lower()]
            for error_line in error_lines[:5]:  # Show first 5 error lines
                print(f"   {error_line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Script execution timed out")
        return False
    except Exception as e:
        print(f"❌ Error running notebook: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists('test_script.py'):
            try:
                os.remove('test_script.py')
            except:
                pass

if __name__ == "__main__":
    print("Testing the fixed notebook with aggregation logic...")
    success = run_notebook_and_show_results()
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    
    if success:
        print("✅ Notebook execution completed!")
        print("📁 Check 'full_execution_output.txt' for complete details")
    else:
        print("❌ Notebook execution had issues")
        print("📁 Check 'full_execution_output.txt' for error details")
    
    print("\n🎯 Expected result: ads_unmelted_v3.csv should have ~873 rows")
    print("   (167 fewer than ads_melted_v3.csv due to aggregation)")
